# 🐳 Docker Quick Start Guide

Get the Enterprise Fund Flow Fraud Detection System running with Dock<PERSON> in minutes!

## ⚡ Super Quick Start

```bash
# 1. Build the image
./docker-build.sh --local-amd64

# 2. Run the container (integrated API and web interface)
docker run -p 8000:8000 fraud-detection:latest

# 3. Open your browser
open http://localhost:8000/docs  # API Documentation
open http://localhost:8000/fraud_detection_web.html  # Web Interface
```

That's it! The integrated service is now running with both API and web interface on the same port.

## 🚀 Docker Compose (Recommended)

For a complete setup with model training and persistence:

```bash
# 1. Copy environment template
cp .env.example .env

# 2. Start all services
docker-compose up

# 3. Access the application
# API: http://localhost:8000
# API Docs: http://localhost:8000/docs
# Web Interface: http://localhost:8000/fraud_detection_web.html
```

## 🛠️ Development Mode

```bash
# Start development environment with live code reloading
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Your code changes will be reflected immediately!
```

## 🧪 Test Everything

```bash
# Run automated tests
./docker-test.sh

# Or just quick tests
./docker-test.sh --quick
```

## 🌍 Multi-Platform Build

```bash
# Build for both AMD64 and ARM64
./docker-build.sh --multi-push --registry your-registry.com/

# Or build locally for your platform
./docker-build.sh --local-amd64  # Intel/AMD
./docker-build.sh --local-arm64  # Apple Silicon
```

## 📋 Common Commands

### Basic Operations
```bash
# Build image
./docker-build.sh --local-amd64

# Run integrated service
docker run -p 8000:8000 fraud-detection:latest

# Run with environment variables
docker run -p 8000:8000 -e SILICONFLOW_API_TOKEN="your-token" fraud-detection:latest

# Run in background
docker run -d -p 8000:8000 --name fraud-service fraud-detection:latest
```

### Docker Compose
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f fraud-api

# Stop services
docker-compose down

# Rebuild and restart
docker-compose up --build
```

### Debugging
```bash
# Check container status
docker ps

# View logs
docker logs <container-id>

# Execute commands in container
docker exec -it <container-id> bash

# Check health
curl http://localhost:8000/health
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file from the template:
```bash
cp .env.example .env
```

Key variables:
- `SILICONFLOW_API_TOKEN` - For LLM features
- `DEFAULT_BATCH_SIZE` - Data generation batch size
- `DEFAULT_FRAUD_RATIO` - Fraud percentage in generated data

### Volume Mounts
```bash
# Persist models and data
docker run -p 8000:8000 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/data:/app/data \
  fraud-detection:latest
```

## 🎯 Use Cases

### 1. Integrated Service (API + Web Interface)
```bash
docker run -p 8000:8000 fraud-detection:latest
```

### 2. Train Model First
```bash
docker run fraud-detection:latest python whole.py 1
```

### 3. Interactive Mode
```bash
docker run -it fraud-detection:latest python start_system.py
```

## 🚨 Troubleshooting

### Build Issues
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache -t fraud-detection:latest .
```

### Permission Issues
```bash
# Fix volume permissions
sudo chown -R 1000:1000 ./models ./data
```

### Memory Issues
```bash
# Run with memory limit
docker run --memory=4g -p 8000:8000 fraud-detection:latest
```

### Port Conflicts
```bash
# Use different port
docker run -p 8080:8000 fraud-detection:latest
```

## 📚 More Information

- **Full Documentation**: See `DOCKER.md` for comprehensive guide
- **API Documentation**: http://localhost:8000/docs (when running)
- **Health Check**: http://localhost:8000/health
- **Project README**: See main `README.md` for application details

## 🎉 Success!

If you can access http://localhost:8000/docs (API) and http://localhost:8000/fraud_detection_web.html (Web Interface), you're all set!

The fraud detection system is now running in Docker and ready to:
- ✅ Detect fraudulent transactions
- ✅ Generate synthetic data
- ✅ Train machine learning models
- ✅ Serve predictions via REST API

Happy fraud detecting! 🕵️‍♂️
