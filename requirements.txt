# 虚假关联交易检测MVP依赖包

# 基础数据科学库
pandas>=1.3.0
numpy>=1.21.0
scikit-learn>=1.0.0
xgboost>=1.6.0

# API服务依赖
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0

# 模型保存
joblib>=1.1.0

# HTTP请求（LLM功能）
requests>=2.25.0

# 开发工具（可选）
pytest>=6.0.0
python-multipart>=0.0.5

# 使用说明
# 安装命令: pip install -r requirements.txt
# 
# 如果只需要基础功能，可以只安装：
# pip install pandas numpy scikit-learn xgboost joblib
#
# 如果需要API服务，额外安装：
# pip install fastapi uvicorn pydantic
#
# 如果需要LLM增强功能，额外安装：
# pip install requests
