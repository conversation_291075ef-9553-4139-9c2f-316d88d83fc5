<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业资金流向欺诈检测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }

        .status-indicator.online {
            background: #28a745;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .panel h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #495057;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            display: inline-block;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-block {
            width: 100%;
            margin-bottom: 10px;
        }

        .result-panel {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .result-low {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result-medium {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .result-high {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .quick-fill {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .quick-fill .btn {
            padding: 8px 12px;
            font-size: 14px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .model-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .model-info h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .info-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .info-item .label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-item .value {
            font-weight: bold;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 企业资金流向欺诈检测系统</h1>
            <p>基于机器学习的智能风险识别平台</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="apiStatus"></div>
                <span id="apiStatusText">检查API状态中...</span>
            </div>
            <div class="status-item">
                <span>📊 模型版本: <strong id="modelVersion">加载中...</strong></span>
            </div>
            <div class="status-item">
                <span>🕒 <span id="currentTime"></span></span>
            </div>
        </div>

        <div class="main-content">
            <!-- 输入面板 -->
            <div class="panel">
                <h2>📝 企业特征数据输入</h2>
                
                <div class="model-info" id="modelInfo" style="display: none;">
                    <h3>🤖 模型信息</h3>
                    <div class="info-grid" id="modelInfoGrid">
                        <!-- 动态填充模型信息 -->
                    </div>
                </div>

                <div class="quick-fill">
                    <button class="btn btn-secondary" onclick="fillNormalCompany()">正常企业</button>
                    <button class="btn btn-secondary" onclick="fillSuspiciousCompany()">可疑企业</button>
                    <button class="btn btn-secondary" onclick="fillHighRiskCompany()">高风险企业</button>
                    <button class="btn btn-secondary" onclick="clearForm()">清空表单</button>
                </div>

                <form id="predictionForm">
                    <div class="form-group">
                        <label for="company_code">企业代码 *</label>
                        <input type="text" id="company_code" name="company_code" placeholder="例: COM0001" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="total_transactions">总交易次数</label>
                            <input type="number" id="total_transactions" name="total_transactions" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label for="total_amount">总交易金额</label>
                            <input type="number" id="total_amount" name="total_amount" min="0" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="avg_amount">平均交易金额</label>
                            <input type="number" id="avg_amount" name="avg_amount" min="0" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="max_amount">最大交易金额</label>
                            <input type="number" id="max_amount" name="max_amount" min="0" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="min_amount">最小交易金额</label>
                            <input type="number" id="min_amount" name="min_amount" min="0" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="amount_std">金额标准差</label>
                            <input type="number" id="amount_std" name="amount_std" min="0" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="unique_partners">交易对手数量</label>
                            <input type="number" id="unique_partners" name="unique_partners" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label for="circular_partners">循环交易对手数</label>
                            <input type="number" id="circular_partners" name="circular_partners" min="0" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="quarter_end_ratio">季末交易比例 (%)</label>
                            <input type="number" id="quarter_end_ratio" name="quarter_end_ratio" min="0" max="100" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="weekend_ratio">周末交易比例 (%)</label>
                            <input type="number" id="weekend_ratio" name="weekend_ratio" min="0" max="100" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="high_amount_ratio">高额交易比例 (%)</label>
                            <input type="number" id="high_amount_ratio" name="high_amount_ratio" min="0" max="100" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="concentration_score">交易集中度</label>
                            <input type="number" id="concentration_score" name="concentration_score" min="0" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="receivable_balance">应收款余额</label>
                            <input type="number" id="receivable_balance" name="receivable_balance" min="0" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="payable_balance">应付款余额</label>
                            <input type="number" id="payable_balance" name="payable_balance" min="0" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="guarantee_amount">担保金额</label>
                            <input type="number" id="guarantee_amount" name="guarantee_amount" min="0" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="internal_ratio">内部交易比例 (%)</label>
                            <input type="number" id="internal_ratio" name="internal_ratio" min="0" max="100" step="0.01" value="0">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">🔍 开始检测</button>
                </form>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>模型分析中，请稍候...</p>
                </div>
            </div>

            <!-- 结果面板 -->
            <div class="panel">
                <h2>📊 检测结果</h2>
                
                <div id="noResult" style="text-align: center; color: #6c757d; padding: 40px;">
                    <p>🤖 请在左侧输入企业特征数据，点击"开始检测"查看结果</p>
                </div>

                <div class="result-panel" id="resultPanel">
                    <div id="resultContent">
                        <!-- 结果内容将在这里动态填充 -->
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <h3>💡 使用提示</h3>
                    <ul style="line-height: 1.6; color: #6c757d; padding-left: 20px;">
                        <li>使用"快速填充"按钮可以自动填入示例数据</li>
                        <li>比例数据请输入0-100之间的数值</li>
                        <li>金额字段支持小数，系统会自动四舍五入</li>
                        <li>企业代码格式建议：COM0001-COM0100</li>
                        <li>检测结果包含欺诈概率、风险等级等信息</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API基础配置 - 使用相对路径，与FastAPI服务在同一端口
        const API_BASE_URL = '';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            checkAPIStatus();
            loadModelInfo();
            
            // 表单提交事件
            document.getElementById('predictionForm').addEventListener('submit', handleSubmit);
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }

        // 检查API状态
        async function checkAPIStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    document.getElementById('apiStatus').classList.add('online');
                    document.getElementById('apiStatusText').textContent = '🟢 API服务正常';
                } else {
                    throw new Error('API不健康');
                }
            } catch (error) {
                document.getElementById('apiStatus').classList.remove('online');
                document.getElementById('apiStatusText').textContent = '🔴 API服务离线';
                console.error('API状态检查失败:', error);
            }
        }

        // 加载模型信息
        async function loadModelInfo() {
            try {
                const response = await fetch(`${API_BASE_URL}/model/info`);
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('modelVersion').textContent = data.model_type || 'XGBoost';
                    
                    // 显示详细模型信息
                    const modelInfo = document.getElementById('modelInfo');
                    const infoGrid = document.getElementById('modelInfoGrid');
                    
                    infoGrid.innerHTML = `
                        <div class="info-item">
                            <div class="label">模型类型</div>
                            <div class="value">${data.model_type || 'XGBoost'}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">特征数量</div>
                            <div class="value">${data.feature_count || 0}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">加载时间</div>
                            <div class="value">${new Date(data.loaded_time).toLocaleTimeString()}</div>
                        </div>
                    `;
                    
                    modelInfo.style.display = 'block';
                } else {
                    document.getElementById('modelVersion').textContent = '未加载';
                }
            } catch (error) {
                document.getElementById('modelVersion').textContent = '加载失败';
                console.error('模型信息加载失败:', error);
            }
        }

        // 表单提交处理
        async function handleSubmit(event) {
            event.preventDefault();
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultPanel').style.display = 'none';
            document.getElementById('noResult').style.display = 'none';
            
            try {
                // 收集表单数据
                const formData = new FormData(event.target);
                const data = {};
                
                for (let [key, value] of formData.entries()) {
                    // 转换数值类型
                    if (key === 'company_code') {
                        data[key] = value;
                    } else {
                        data[key] = parseFloat(value) || 0;
                    }
                    
                    // 比例字段转换为小数
                    if (key.includes('ratio')) {
                        data[key] = data[key] / 100;
                    }
                }
                
                // 调用预测API
                const response = await fetch(`${API_BASE_URL}/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResult(result);
                } else {
                    throw new Error(result.detail || '预测失败');
                }
                
            } catch (error) {
                console.error('预测失败:', error);
                displayError(error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // 显示预测结果
        function displayResult(result) {
            const resultPanel = document.getElementById('resultPanel');
            const resultContent = document.getElementById('resultContent');
            
            // 确定风险等级样式
            let riskClass = 'result-low';
            let riskIcon = '✅';
            let riskColor = '#28a745';
            
            if (result.fraud_probability >= 0.7) {
                riskClass = 'result-high';
                riskIcon = '🚨';
                riskColor = '#dc3545';
            } else if (result.fraud_probability >= 0.3) {
                riskClass = 'result-medium';
                riskIcon = '⚠️';
                riskColor = '#ffc107';
            }
            
            resultContent.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 3rem;">${riskIcon}</div>
                    <h3 style="color: ${riskColor}; margin: 10px 0;">
                        ${result.risk_level} 风险企业
                    </h3>
                </div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="label">企业代码</div>
                        <div class="value">${result.company_code}</div>
                    </div>
                    <div class="info-item">
                        <div class="label">欺诈概率</div>
                        <div class="value" style="color: ${riskColor}">
                            ${(result.fraud_probability * 100).toFixed(2)}%
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="label">预测标签</div>
                        <div class="value">
                            ${result.predicted_label === 1 ? '🔴 欺诈' : '🟢 正常'}
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="label">是否可疑</div>
                        <div class="value">
                            ${result.is_suspicious ? '⚠️ 是' : '✅ 否'}
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4>🔍 风险分析说明</h4>
                    <ul style="margin: 10px 0 0 20px; line-height: 1.6;">
                        ${generateRiskExplanation(result)}
                    </ul>
                </div>
                
                <div style="margin-top: 15px; font-size: 14px; color: #6c757d; text-align: center;">
                    预测时间: ${new Date(result.prediction_time).toLocaleString('zh-CN')}
                </div>
            `;
            
            resultPanel.className = `result-panel ${riskClass}`;
            resultPanel.style.display = 'block';
        }

        // 生成风险说明
        function generateRiskExplanation(result) {
            const explanations = [];
            
            if (result.fraud_probability >= 0.7) {
                explanations.push('<li>欺诈概率极高，建议立即进行详细调查</li>');
                explanations.push('<li>该企业的交易模式存在明显异常特征</li>');
                explanations.push('<li>建议暂停相关业务往来，等待进一步核实</li>');
            } else if (result.fraud_probability >= 0.3) {
                explanations.push('<li>存在一定风险，建议加强监控</li>');
                explanations.push('<li>部分交易特征显示潜在异常</li>');
                explanations.push('<li>建议进行进一步的人工审核</li>');
            } else {
                explanations.push('<li>风险较低，交易模式相对正常</li>');
                explanations.push('<li>可以继续正常业务往来</li>');
                explanations.push('<li>建议保持常规监控</li>');
            }
            
            return explanations.join('');
        }

        // 显示错误信息
        function displayError(errorMessage) {
            const resultPanel = document.getElementById('resultPanel');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 3rem; color: #dc3545;">❌</div>
                    <h3 style="color: #dc3545; margin: 10px 0;">预测失败</h3>
                    <p style="color: #6c757d;">${errorMessage}</p>
                    <div style="margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="checkAPIStatus()">重新检查API状态</button>
                    </div>
                </div>
            `;
            
            resultPanel.className = 'result-panel result-high';
            resultPanel.style.display = 'block';
        }

        // 快速填充函数
        function fillNormalCompany() {
            document.getElementById('company_code').value = 'COM0001';
            document.getElementById('total_transactions').value = '45';
            document.getElementById('total_amount').value = '2350000.00';
            document.getElementById('avg_amount').value = '52222.22';
            document.getElementById('max_amount').value = '150000.00';
            document.getElementById('min_amount').value = '5000.00';
            document.getElementById('amount_std').value = '35000.00';
            document.getElementById('unique_partners').value = '12';
            document.getElementById('circular_partners').value = '0';
            document.getElementById('quarter_end_ratio').value = '8.5';
            document.getElementById('weekend_ratio').value = '12.3';
            document.getElementById('high_amount_ratio').value = '15.2';
            document.getElementById('concentration_score').value = '0.25';
            document.getElementById('receivable_balance').value = '450000.00';
            document.getElementById('payable_balance').value = '320000.00';
            document.getElementById('guarantee_amount').value = '0.00';
            document.getElementById('internal_ratio').value = '5.5';
        }

        function fillSuspiciousCompany() {
            document.getElementById('company_code').value = 'COM0005';
            document.getElementById('total_transactions').value = '28';
            document.getElementById('total_amount').value = '5500000.00';
            document.getElementById('avg_amount').value = '196428.57';
            document.getElementById('max_amount').value = '999999.00';
            document.getElementById('min_amount').value = '50000.00';
            document.getElementById('amount_std').value = '250000.00';
            document.getElementById('unique_partners').value = '5';
            document.getElementById('circular_partners').value = '3';
            document.getElementById('quarter_end_ratio').value = '45.2';
            document.getElementById('weekend_ratio').value = '25.8';
            document.getElementById('high_amount_ratio').value = '65.5';
            document.getElementById('concentration_score').value = '0.85';
            document.getElementById('receivable_balance').value = '2500000.00';
            document.getElementById('payable_balance').value = '2300000.00';
            document.getElementById('guarantee_amount').value = '1000000.00';
            document.getElementById('internal_ratio').value = '75.5';
        }

        function fillHighRiskCompany() {
            document.getElementById('company_code').value = 'COM0010';
            document.getElementById('total_transactions').value = '15';
            document.getElementById('total_amount').value = '9999999.00';
            document.getElementById('avg_amount').value = '666666.60';
            document.getElementById('max_amount').value = '2000000.00';
            document.getElementById('min_amount').value = '100000.00';
            document.getElementById('amount_std').value = '500000.00';
            document.getElementById('unique_partners').value = '3';
            document.getElementById('circular_partners').value = '3';
            document.getElementById('quarter_end_ratio').value = '80.0';
            document.getElementById('weekend_ratio').value = '40.0';
            document.getElementById('high_amount_ratio').value = '90.0';
            document.getElementById('concentration_score').value = '0.95';
            document.getElementById('receivable_balance').value = '8000000.00';
            document.getElementById('payable_balance').value = '7500000.00';
            document.getElementById('guarantee_amount').value = '5000000.00';
            document.getElementById('internal_ratio').value = '95.0';
        }

        function clearForm() {
            document.getElementById('predictionForm').reset();
            document.getElementById('resultPanel').style.display = 'none';
            document.getElementById('noResult').style.display = 'block';
        }
    </script>
</body>
</html>
