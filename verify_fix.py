#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API修复验证脚本
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_api_endpoints():
    """测试API端点"""
    print("🔧 API修复验证测试")
    print("=" * 50)
    
    # 测试1: 健康检查
    print("\n1️⃣ 测试健康检查...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except requests.RequestException as e:
        print(f"❌ 无法连接API服务: {e}")
        print("   请确保API服务正在运行: python fraud_api.py")
        return
    
    # 测试2: 模型信息
    print("\n2️⃣ 测试模型信息端点...")
    try:
        response = requests.get(f"{API_BASE_URL}/model/info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 模型信息获取成功")
            print(f"   模型类型: {data.get('model_type')}")
            print(f"   特征数量: {data.get('feature_count')}")
            print(f"   加载时间: {data.get('loaded_time')}")
        else:
            error_detail = response.json().get('detail', '未知错误')
            print(f"⚠️ 模型信息获取失败: {error_detail}")
    except Exception as e:
        print(f"❌ 模型信息测试失败: {e}")
    
    # 测试3: 预测端点
    print("\n3️⃣ 测试预测端点...")
    test_data = {
        "company_code": "COM0001",
        "total_transactions": 45,
        "total_amount": 2350000.00,
        "avg_amount": 52222.22,
        "max_amount": 150000.00,
        "min_amount": 5000.00,
        "amount_std": 35000.00,
        "unique_partners": 12,
        "circular_partners": 0,
        "quarter_end_ratio": 0.085,
        "weekend_ratio": 0.123,
        "high_amount_ratio": 0.152,
        "concentration_score": 0.25,
        "receivable_balance": 450000.00,
        "payable_balance": 320000.00,
        "guarantee_amount": 0.00,
        "internal_ratio": 0.055
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/predict", 
            json=test_data, 
            timeout=10,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            print("✅ 预测测试成功")
            print(f"   企业代码: {data.get('company_code')}")
            print(f"   欺诈概率: {data.get('fraud_probability'):.4f}")
            print(f"   风险等级: {data.get('risk_level')}")
            print(f"   是否可疑: {data.get('is_suspicious')}")
        else:
            error_detail = response.json().get('detail', '未知错误')
            print(f"⚠️ 预测测试失败: {error_detail}")
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API修复验证完成！")
    print("\n💡 如果所有测试都通过，说明numpy序列化问题已解决")

if __name__ == "__main__":
    test_api_endpoints()
