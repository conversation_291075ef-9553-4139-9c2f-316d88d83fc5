# 🚀 Dual-Service Docker Setup

The Docker container now runs **both** the FastAPI backend and a static web server simultaneously, providing a complete single-container solution.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────┐
│           Docker Container             │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   FastAPI   │  │  Static Web     │   │
│  │   Service   │  │    Server       │   │
│  │             │  │                 │   │
│  │ Port 8000   │  │   Port 9000     │   │
│  └─────────────┘  └─────────────────┘   │
│         │                   │           │
│         │                   │           │
│  ┌─────────────────────────────────────┐ │
│  │      start-services.sh              │ │
│  │   (Process Manager Script)          │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🎯 What's Included

### Port 8000 - FastAPI Service
- **API Endpoints**: All fraud detection API functionality
- **Interactive Docs**: Swagger UI at `/docs`
- **Health Check**: Status endpoint at `/health`
- **Model Operations**: Prediction, training, batch processing

### Port 9000 - Static Web Server
- **Web Interface**: `fraud_detection_web.html`
- **Demo Interface**: `fraud_detection_demo.html`
- **Static Files**: All HTML, CSS, JS files served directly
- **CORS Enabled**: Cross-origin requests allowed

## 🔧 Implementation Details

### Startup Script (`start-services.sh`)
- Starts FastAPI service in background
- Starts Python HTTP server for static files
- Monitors both processes
- Handles graceful shutdown
- Provides colored logging output

### Process Management
- Both services run concurrently
- Automatic restart if either service fails
- Signal handling for clean shutdown
- Health monitoring and status reporting

### Docker Configuration
- **Exposed Ports**: 8000 and 9000
- **Health Check**: Tests both services
- **Non-root User**: Security best practices
- **Volume Support**: Persistent models and data

## 🚀 Usage Examples

### Basic Usage
```bash
# Start both services
docker run -p 8000:8000 -p 9000:9000 fraud-detection:latest

# Access the services
curl http://localhost:8000/health          # API health check
curl http://localhost:9000/                # Web server root
open http://localhost:8000/docs            # API documentation
open http://localhost:9000/fraud_detection_web.html  # Web interface
```

### Docker Compose
```bash
# Start with compose
docker-compose up

# Services available at:
# - API: http://localhost:8000
# - Web: http://localhost:9000
```

### Development Mode
```bash
# Mount source code for development
docker run -p 8000:8000 -p 9000:9000 \
  -v $(pwd):/app \
  fraud-detection:latest
```

## 🔍 Service Discovery

### API Service Endpoints
- `GET /` - Service information
- `GET /health` - Health check
- `GET /docs` - Interactive API documentation
- `POST /predict` - Single prediction
- `POST /predict/batch` - Batch predictions
- `POST /train` - Model training
- `GET /model/info` - Model information

### Web Server Files
- `/` - Directory listing
- `/fraud_detection_web.html` - Main web interface
- `/fraud_detection_demo.html` - Demo interface
- `/README.md` - Project documentation
- All other static files in the project directory

## 🛠️ Customization

### Environment Variables
```bash
# Service configuration
API_PORT=8000          # FastAPI port
WEB_PORT=9000          # Web server port
API_HOST=0.0.0.0       # FastAPI bind address
WEB_HOST=0.0.0.0       # Web server bind address

# Application configuration
SILICONFLOW_API_TOKEN=your-token
DEBUG=false
LOG_LEVEL=INFO
```

### Alternative Entry Points
```bash
# API service only
docker run -p 8000:8000 fraud-detection:latest python fraud_api.py

# Custom startup script
docker run -p 8000:8000 -p 9000:9000 fraud-detection:latest /app/start-services.sh

# Interactive mode
docker run -it fraud-detection:latest bash
```

## 🧪 Testing

### Automated Testing
```bash
# Run comprehensive tests
./docker-test.sh

# Quick dual-service test
./test-dual-service.sh
```

### Manual Testing
```bash
# Test API service
curl -X POST http://localhost:8000/predict \
  -H "Content-Type: application/json" \
  -d '{"acntmcode":"COM001","partycode":"COM002","amount":100000,"or_type_fy":"转账","quarter":"Q1","is_holiday":false,"month_day":15,"is_month_end":false}'

# Test web server
curl http://localhost:9000/fraud_detection_web.html
```

## 🔧 Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check container logs
docker logs <container-name>

# Check specific service logs
docker exec <container-name> ps aux  # See running processes
```

#### Port Conflicts
```bash
# Use different ports
docker run -p 8080:8000 -p 9090:9000 fraud-detection:latest
```

#### Web Files Not Found
```bash
# Verify files are in container
docker exec <container-name> ls -la /app/*.html

# Check web server logs
docker logs <container-name> | grep "\[WEB\]"
```

### Health Checks
```bash
# Check both services
curl http://localhost:8000/health && curl http://localhost:9000/

# Container health status
docker ps  # Shows health status
```

## 🎉 Benefits

### Single Container Solution
- ✅ No need for separate containers
- ✅ Simplified deployment
- ✅ Reduced resource usage
- ✅ Easier networking

### Complete Functionality
- ✅ Full API access
- ✅ Web interface included
- ✅ No external dependencies
- ✅ Ready for production

### Developer Friendly
- ✅ Easy local development
- ✅ Hot reloading support
- ✅ Comprehensive testing
- ✅ Clear documentation

This dual-service setup provides the best of both worlds: a powerful API backend and an intuitive web interface, all in a single, easy-to-deploy Docker container! 🐳
