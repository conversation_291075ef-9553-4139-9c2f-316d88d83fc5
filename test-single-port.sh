#!/bin/bash
# Quick test script for the dual-service Docker container
# Tests both FastAPI and static web server functionality

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
CONTAINER_NAME="fraud-test-single"
SERVICE_PORT="8000"

cleanup() {
    print_info "Cleaning up..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
}

# Cleanup on exit
trap cleanup EXIT

print_info "Testing Single-Port Docker Container"
print_info "===================================="

# Build the image if it doesn't exist
if ! docker image inspect fraud-detection:latest >/dev/null 2>&1; then
    print_info "Building Docker image..."
    ./docker-build.sh --local-amd64
fi

# Start the container
print_info "Starting container with integrated service..."
docker run -d \
    --name $CONTAINER_NAME \
    -p $SERVICE_PORT:8000 \
    fraud-detection:latest

# Wait for services to start
print_info "Waiting for services to start..."
sleep 10

# Test API service
print_info "Testing FastAPI service..."
if curl -s -f "http://localhost:$SERVICE_PORT/health" >/dev/null; then
    print_success "FastAPI service is responding"
else
    print_error "FastAPI service is not responding"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test API docs
if curl -s -f "http://localhost:$SERVICE_PORT/docs" >/dev/null; then
    print_success "API documentation is accessible"
else
    print_warning "API documentation is not accessible"
fi

# Test static file serving
print_info "Testing integrated static file serving..."
if curl -s -f "http://localhost:$SERVICE_PORT/" >/dev/null; then
    print_success "FastAPI root is responding"
else
    print_error "FastAPI root is not responding"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test specific HTML files
html_files=("fraud_detection_web.html" "fraud_detection_demo.html")
for file in "${html_files[@]}"; do
    if curl -s -f "http://localhost:$SERVICE_PORT/$file" >/dev/null; then
        print_success "Web file accessible: $file"
    else
        print_warning "Web file not found: $file"
    fi
done

# Show container logs
print_info "Container logs (last 20 lines):"
docker logs --tail 20 $CONTAINER_NAME

print_success "Single-port service test completed successfully!"
echo
print_info "Access URLs:"
echo "  - API Service: http://localhost:$SERVICE_PORT"
echo "  - API Docs: http://localhost:$SERVICE_PORT/docs"
echo "  - Web Interface: http://localhost:$SERVICE_PORT/fraud_detection_web.html"
echo "  - Demo Interface: http://localhost:$SERVICE_PORT/fraud_detection_demo.html"
echo "  - Static Files: http://localhost:$SERVICE_PORT/static/"
echo
print_info "Container will be cleaned up automatically."
