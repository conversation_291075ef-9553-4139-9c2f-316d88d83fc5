# LLM增强数据生成配置文件
# 请在使用前配置正确的API Token

# SiliconFlow API配置
SILICONFLOW_API_TOKEN = "sk-dceyienvrazgavqubkwwxtlmsbseqlgmswqlowjfbbiyeenl"  # 请替换为您的实际Token
SILICONFLOW_API_URL = "https://api.siliconflow.cn/v1/chat/completions"
SILICONFLOW_MODEL = "Qwen/QwQ-32B"

# 数据生成参数
DEFAULT_BATCH_SIZE = 20  # 每批次生成的数据量
DEFAULT_TOTAL_SIZE = 2000  # 默认总数据量
DEFAULT_FRAUD_RATIO = 0.05  # 默认欺诈比例
API_RETRY_COUNT = 3  # API重试次数
API_TIMEOUT = 30  # API超时时间(秒)

# 数据质量参数
MIN_AMOUNT = 10000  # 最小交易金额
MAX_AMOUNT = 5000000  # 最大交易金额
COMPANY_COUNT = 100  # 企业数量

# 模型参数
TEMPERATURE = 0.8  # 生成随机性，范围0-1
MAX_TOKENS = 1000  # 最大生成token数

# 输出配置
SAVE_LLM_METADATA = True  # 是否保存LLM生成的元数据
VERBOSE_OUTPUT = True  # 是否输出详细信息

# 使用说明
"""
使用步骤：
1. 将 SILICONFLOW_API_TOKEN 替换为您的实际API Token
2. 根据需要调整数据生成参数
3. 运行 whole.py 并选择LLM增强模式

获取API Token：
1. 访问 https://siliconflow.cn/
2. 注册账户并获取API Key
3. 将Key配置到上面的 SILICONFLOW_API_TOKEN 字段

注意事项：
- API有调用频率限制，如遇到429错误请稍后重试
- 生成大量数据时建议适当减小 DEFAULT_BATCH_SIZE
- 确保网络连接稳定
"""
