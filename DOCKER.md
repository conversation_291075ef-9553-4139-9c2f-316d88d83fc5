# Docker Deployment Guide

This guide provides comprehensive instructions for building and deploying the Enterprise Fund Flow Fraud Detection System using Docker with multi-platform support.

## 🏗️ Architecture Overview

The Docker setup includes:
- **Multi-stage build** for optimized image size
- **Multi-platform support** for `linux/amd64` and `linux/arm64`
- **Security best practices** with non-root user
- **Flexible entry points** for different use cases

## 📋 Prerequisites

### Required Software
- Docker Desktop 20.10+ or Docker Engine with buildx plugin
- For multi-platform builds: Docker buildx enabled

### Verify Prerequisites
```bash
# Check Docker version
docker --version

# Check buildx availability
docker buildx version

# List available builders
docker buildx ls
```

## 🚀 Quick Start

### 1. Build for Local Platform (Fastest)
```bash
# Build for current platform (AMD64 or ARM64)
./docker-build.sh --local-amd64

# Or for ARM64
./docker-build.sh --local-arm64
```

### 2. Run the Container
```bash
# Run API service
docker run -p 8000:8000 fraud-detection:latest

# Run with environment variables
docker run -p 8000:8000 \
  -e SILICONFLOW_API_TOKEN="your-token" \
  fraud-detection:latest

# Run in background
docker run -d -p 8000:8000 --name fraud-api fraud-detection:latest
```

### 3. Access the Application
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **API Root**: http://localhost:8000/

## 🔧 Build Options

### Local Development Builds
```bash
# AMD64 only (Intel/AMD processors)
./docker-build.sh --local-amd64

# ARM64 only (Apple Silicon, ARM servers)
./docker-build.sh --local-arm64

# Custom tag
./docker-build.sh --local-amd64 --tag v1.0.0
```

### Multi-Platform Builds
```bash
# Build for both platforms (requires registry)
./docker-build.sh --multi-push --registry your-registry.com/

# With custom tag
./docker-build.sh --multi-push --tag v1.0.0 --registry your-registry.com/
```

### Manual Docker Commands
```bash
# Single platform build
docker build -t fraud-detection:latest .

# Multi-platform build with buildx
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --tag your-registry.com/fraud-detection:latest \
  --push .
```

## 🐳 Container Usage

### Different Entry Points
```bash
# Default: API service
docker run -p 8000:8000 fraud-detection:latest

# Train model first, then start API
docker run -p 8000:8000 fraud-detection:latest python whole.py 1

# Web server with static files
docker run -p 9000:9000 fraud-detection:latest python web_server.py

# Interactive mode
docker run -it fraud-detection:latest python start_system.py

# Custom command
docker run -it fraud-detection:latest bash
```

### Environment Variables
```bash
# LLM Configuration
-e SILICONFLOW_API_TOKEN="your-api-token"
-e SILICONFLOW_API_URL="https://api.siliconflow.cn/v1/chat/completions"
-e SILICONFLOW_MODEL="Qwen/QwQ-32B"

# Application Configuration
-e PYTHONPATH="/app"
-e PYTHONUNBUFFERED="1"
```

### Volume Mounts
```bash
# Persist model files
docker run -p 8000:8000 \
  -v $(pwd)/models:/app/models \
  fraud-detection:latest

# Mount data directory
docker run -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/models:/app/models \
  fraud-detection:latest

# Development mode (mount source code)
docker run -p 8000:8000 \
  -v $(pwd):/app \
  fraud-detection:latest
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache -t fraud-detection:latest .
```

#### 2. Multi-platform Build Issues
```bash
# Reset buildx
docker buildx rm multiplatform-builder
docker buildx create --name multiplatform-builder --driver docker-container --bootstrap
docker buildx use multiplatform-builder
```

#### 3. Permission Issues
```bash
# The container runs as non-root user 'appuser'
# Ensure mounted volumes have correct permissions
sudo chown -R 1000:1000 ./models ./data
```

#### 4. Memory Issues
```bash
# Increase Docker memory limit in Docker Desktop settings
# Or run with memory limit
docker run --memory=4g -p 8000:8000 fraud-detection:latest
```

### Health Checks
```bash
# Check container health
docker ps
docker logs <container-id>

# Manual health check
curl http://localhost:8000/health

# Container inspection
docker exec -it <container-id> bash
```

## 🚀 Production Deployment

### Registry Push
```bash
# Tag for registry
docker tag fraud-detection:latest your-registry.com/fraud-detection:v1.0.0

# Push to registry
docker push your-registry.com/fraud-detection:v1.0.0

# Multi-platform push
./docker-build.sh --multi-push --tag v1.0.0 --registry your-registry.com/
```

### Security Considerations
- Container runs as non-root user
- Minimal base image (python:3.11-slim)
- No unnecessary packages in runtime image
- Health checks enabled
- Use secrets management for API tokens

### Performance Optimization
- Multi-stage build reduces image size
- Layer caching optimized
- Virtual environment for clean dependencies
- .dockerignore excludes unnecessary files

## 📊 Image Information

### Image Sizes (Approximate)
- **Build stage**: ~1.2GB (includes build tools)
- **Runtime stage**: ~400-500MB (optimized)
- **Compressed**: ~150-200MB (when pushed to registry)

### Supported Platforms
- `linux/amd64` (Intel/AMD x86_64)
- `linux/arm64` (ARM64/Apple Silicon)

### Base Images
- **Build**: `python:3.11` (full Python with build tools)
- **Runtime**: `python:3.11-slim` (minimal Python runtime)

## 🧪 Testing and Validation

### Automated Testing
```bash
# Run full test suite
./docker-test.sh

# Quick tests only
./docker-test.sh --quick

# Build and test
./docker-test.sh --build-test
```

### Manual Testing
```bash
# 1. Build the image
./docker-build.sh --local-amd64

# 2. Run container
docker run -d -p 8000:8000 --name fraud-test fraud-detection:latest

# 3. Test endpoints
curl http://localhost:8000/health
curl http://localhost:8000/docs
curl -X POST http://localhost:8000/predict \
  -H "Content-Type: application/json" \
  -d '{"acntmcode":"COM001","partycode":"COM002","amount":100000,"or_type_fy":"转账","quarter":"Q1","is_holiday":false,"month_day":15,"is_month_end":false}'

# 4. Check logs
docker logs fraud-test

# 5. Cleanup
docker stop fraud-test && docker rm fraud-test
```

### Docker Compose Testing
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs fraud-api

# Test API
curl http://localhost:8000/health

# Stop services
docker-compose down
```

### Development Mode Testing
```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# With specific profiles
docker-compose -f docker-compose.yml -f docker-compose.dev.yml --profile database up
```

## 🔗 Related Files

- `Dockerfile` - Multi-stage Docker configuration
- `.dockerignore` - Build context optimization
- `docker-build.sh` - Multi-platform build script
- `docker-test.sh` - Automated testing script
- `docker-compose.yml` - Production setup
- `docker-compose.dev.yml` - Development overrides
- `.env.example` - Environment configuration template
- `requirements.txt` - Python dependencies
