import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import string

class MVPDataGenerator:
    def __init__(self, seed=42):
        np.random.seed(seed)
        random.seed(seed)
        
        # 扩大企业池到500家，增加样本多样性
        self.companies = [f'COM{str(i).zfill(4)}' for i in range(1, 501)]  # 500家企业
        self.company_names = [f'企业{chr(65+i%26)}{str(i//26+1)}' for i in range(500)]
        
        # 增加更多欺诈组合，模拟复杂关联网络
        self.fraud_groups = [
            ['COM0001', 'COM0002', 'COM0003'],  # 三角循环
            ['COM0004', 'COM0005'],             # 双边对倒
            ['COM0006', 'COM0007', 'COM0008', 'COM0009'],  # 多方循环
            ['COM0010', 'COM0011'],             # 快速往返
            ['COM0012', 'COM0013', 'COM0014'],  # 延迟循环
            ['COM0015', 'COM0016', 'COM0017', 'COM0018', 'COM0019'],  # 复杂网络
            ['COM0020', 'COM0021', 'COM0022'],  # 季末冲业绩
            ['COM0023', 'COM0024'],             # 隐蔽对倒
            ['COM0025', 'COM0026', 'COM0027'],  # 三方串通
            ['COM0028', 'COM0029', 'COM0030', 'COM0031'],  # 多级嵌套
        ]
        
        # 定义多个季度的时间范围
        self.quarters = [
            (datetime(2024, 1, 1), datetime(2024, 3, 31)),   # Q1
            (datetime(2024, 4, 1), datetime(2024, 6, 30)),   # Q2  
            (datetime(2024, 7, 1), datetime(2024, 9, 30)),   # Q3
            (datetime(2024, 10, 1), datetime(2024, 12, 31)), # Q4
        ]
        
    def generate_settlement_data(self, n_total=5000, fraud_ratio=0.05):
        """生成资金结算数据 - 改进版本"""
        n_fraud = int(n_total * fraud_ratio)
        n_normal = n_total - n_fraud
        
        data = []
        
        # 生成正常交易 (95%) - 增加真实性
        for _ in range(n_normal):
            from_company = random.choice(self.companies)
            to_company = random.choice([c for c in self.companies if c != from_company])
            
            # 随机选择季度和具体日期
            quarter_start, quarter_end = random.choice(self.quarters)
            trade_date = self._random_date_in_range(quarter_start, quarter_end)
            
            # 添加工作日权重 (工作日概率更高)
            if trade_date.weekday() >= 5:  # 周末
                if random.random() < 0.7:  # 70%概率跳过周末交易，增加真实性
                    continue
            
            # 添加数据噪声和缺失值模拟真实环境
            amount = self._generate_normal_amount()
            if random.random() < 0.05:  # 5%概率金额异常
                amount *= random.uniform(0.1, 10)  # 极端金额变化
                amount = round(amount, 2)  # 四舍五入保留两位小数
                
            # 添加缺失和错误数据
            company_name = self.company_names[int(from_company[3:]) - 1]
            receive_name = self.company_names[int(to_company[3:]) - 1]
            
            if random.random() < 0.02:  # 2%概率名称缺失
                company_name = None
            if random.random() < 0.02:
                receive_name = None
            
            data.append({
                'acntmcode': from_company,
                'acntmname': company_name,
                'partycode': to_company,
                'receiveunitname': receive_name,
                'amount': amount,
                'or_type_fy': random.choice(['收入', '支出', '转账', '借款', '还款']),  # 增加类型多样性
                'banktradedare': trade_date,
                'dayidx': trade_date.strftime('%Y-%m-%d'),
                'transaction_type': 'normal',
                'quarter': f"Q{((trade_date.month-1)//3)+1}",
                'is_holiday': trade_date.weekday() >= 5,  # 新增特征
                'month_day': trade_date.day,  # 新增特征
                'is_month_end': trade_date.day >= 25  # 新增特征
            })
        
        # 生成虚假关联交易 (5%) - 但要更隐蔽
        fraud_data = self._generate_fraud_transactions_v2(n_fraud)
        data.extend(fraud_data)
        
        # 在欺诈交易中混入正常交易干扰
        distraction_data = self._generate_distraction_transactions(int(n_fraud * 0.5))
        data.extend(distraction_data)
        
        # 打乱数据顺序，增加真实性
        random.shuffle(data)
        
        return pd.DataFrame(data)
    
    def _generate_fraud_transactions_v2(self, n_fraud):
        """生成改进版虚假关联交易模式 - 更隐蔽"""
        fraud_data = []
        
        # 模式1: 循环资金流 (30%) - 更隐蔽的循环
        for _ in range(int(n_fraud * 0.3)):
            group = random.choice(self.fraud_groups)
            base_amount = round(np.random.uniform(300000, 2000000), 2)  # 降低金额，更隐蔽
            quarter_start, quarter_end = random.choice(self.quarters)
            base_date = self._random_date_in_range(quarter_start, quarter_end)
            
            # 不是所有环节都参与，增加隐蔽性
            participating_companies = random.sample(group, random.randint(2, len(group)))
            
            for i in range(len(participating_companies)):
                from_comp = participating_companies[i]
                to_comp = participating_companies[(i + 1) % len(participating_companies)]
                
                # 大幅增加金额和时间噪声
                amount_noise = 0.5 + 1.0 * random.random()  # 50%-150%变化
                time_noise = random.randint(-30, 30)  # 时间噪声±30天
                actual_amount = round(base_amount * amount_noise, 2)  # 四舍五入保留两位小数
                actual_date = base_date + timedelta(days=time_noise)
                
                fraud_data.append({
                    'acntmcode': from_comp,
                    'acntmname': self.company_names[int(from_comp[3:]) - 1] if int(from_comp[3:]) <= len(self.company_names) else f'企业{from_comp}',
                    'partycode': to_comp,
                    'receiveunitname': self.company_names[int(to_comp[3:]) - 1] if int(to_comp[3:]) <= len(self.company_names) else f'企业{to_comp}',
                    'amount': actual_amount,
                    'or_type_fy': random.choice(['支出', '转账', '往来款', '代付款']),
                    'banktradedare': actual_date,
                    'dayidx': actual_date.strftime('%Y-%m-%d'),
                    'transaction_type': 'fraud_circular',
                    'quarter': f"Q{((actual_date.month-1)//3)+1}",
                    'is_holiday': actual_date.weekday() >= 5,
                    'month_day': actual_date.day,
                    'is_month_end': actual_date.day >= 25
                })
        
        # 模式2: 季末集中但分散到不同日期 (25%)
        for _ in range(int(n_fraud * 0.25)):
            group = random.choice(self.fraud_groups[:5])  # 选择前5个组
            from_company = random.choice(group)
            to_company = random.choice([c for c in group if c != from_company])
            
            # 季末前1-10天，而不是就在季末
            quarter_end_date = self._get_quarter_end_date()
            spread_days = random.randint(1, 10)
            actual_date = quarter_end_date - timedelta(days=spread_days)
            
            fraud_data.append({
                'acntmcode': from_company,
                'acntmname': self.company_names[int(from_company[3:]) - 1] if int(from_company[3:]) <= len(self.company_names) else f'企业{from_company}',
                'partycode': to_company,
                'receiveunitname': self.company_names[int(to_company[3:]) - 1] if int(to_company[3:]) <= len(self.company_names) else f'企业{to_company}',
                'amount': np.random.uniform(400000, 1500000),  # 金额更分散
                'or_type_fy': random.choice(['收入', '支出', '利润分配']),
                'banktradedare': actual_date,
                'dayidx': actual_date.strftime('%Y-%m-%d'),
                'transaction_type': 'fraud_period_end',
                'quarter': f"Q{((actual_date.month-1)//3)+1}",
                'is_holiday': actual_date.weekday() >= 5,
                'month_day': actual_date.day,
                'is_month_end': actual_date.day >= 25
            })
        
        # 模式3: 延迟往返 (25%) - 间隔更长，更难检测
        for _ in range(int(n_fraud * 0.25)):
            group = random.choice(self.fraud_groups[5:])  # 使用后面的组
            if len(group) >= 2:
                comp1, comp2 = random.sample(group, 2)
                amount = round(np.random.uniform(200000, 1200000), 2)  # 降低金额，四舍五入保留两位小数
                quarter_start, quarter_end = random.choice(self.quarters)
                base_date = self._random_date_in_range(quarter_start, quarter_end)
                
                # 往: A->B
                fraud_data.append({
                    'acntmcode': comp1,
                    'acntmname': self.company_names[int(comp1[3:]) - 1] if int(comp1[3:]) <= len(self.company_names) else f'企业{comp1}',
                    'partycode': comp2,
                    'receiveunitname': self.company_names[int(comp2[3:]) - 1] if int(comp2[3:]) <= len(self.company_names) else f'企业{comp2}',
                    'amount': amount,
                    'or_type_fy': '支出',
                    'banktradedare': base_date,
                    'dayidx': base_date.strftime('%Y-%m-%d'),
                    'transaction_type': 'fraud_round_trip_out',
                    'quarter': f"Q{((base_date.month-1)//3)+1}",
                    'is_holiday': base_date.weekday() >= 5,
                    'month_day': base_date.day,
                    'is_month_end': base_date.day >= 25
                })
                
                # 返: B->A (15-90天内，大幅延长间隔)
                return_days = random.randint(15, 90)
                return_date = base_date + timedelta(days=return_days)
                return_amount = amount * (0.85 + 0.25 * random.random())  # 85%-110%，更大变化
                
                fraud_data.append({
                    'acntmcode': comp2,
                    'acntmname': self.company_names[int(comp2[3:]) - 1] if int(comp2[3:]) <= len(self.company_names) else f'企业{comp2}',
                    'partycode': comp1,
                    'receiveunitname': self.company_names[int(comp1[3:]) - 1] if int(comp1[3:]) <= len(self.company_names) else f'企业{comp1}',
                    'amount': return_amount,
                    'or_type_fy': '支出',
                    'banktradedare': return_date,
                    'dayidx': return_date.strftime('%Y-%m-%d'),
                    'transaction_type': 'fraud_round_trip_back',
                    'quarter': f"Q{((return_date.month-1)//3)+1}",
                    'is_holiday': return_date.weekday() >= 5,
                    'month_day': return_date.day,
                    'is_month_end': return_date.day >= 25
                })
        
        # 模式4: 微妙的不平衡循环 (20%) - 最难检测的模式
        for _ in range(int(n_fraud * 0.2)):
            group = random.choice(self.fraud_groups)
            if len(group) >= 3:
                participants = random.sample(group, 3)
                base_amount = round(np.random.uniform(150000, 800000), 2)  # 小额，四舍五入保留两位小数
                quarter_start, quarter_end = random.choice(self.quarters)
                
                # A->B, B->C, C->A，但金额和时间都不对等
                amounts = [
                    round(base_amount * (0.8 + 0.6 * random.random()), 2),
                    round(base_amount * (0.7 + 0.8 * random.random()), 2), 
                    round(base_amount * (0.6 + 0.9 * random.random()), 2)
                ]
                
                for i in range(3):
                    from_comp = participants[i]
                    to_comp = participants[(i + 1) % 3]
                    base_date = self._random_date_in_range(quarter_start, quarter_end)
                    
                    fraud_data.append({
                        'acntmcode': from_comp,
                        'acntmname': self.company_names[int(from_comp[3:]) - 1] if int(from_comp[3:]) <= len(self.company_names) else f'企业{from_comp}',
                        'partycode': to_comp,
                        'receiveunitname': self.company_names[int(to_comp[3:]) - 1] if int(to_comp[3:]) <= len(self.company_names) else f'企业{to_comp}',
                        'amount': amounts[i],
                        'or_type_fy': random.choice(['支出', '投资', '借款', '服务费']),
                        'banktradedare': base_date + timedelta(days=random.randint(-45, 45)),
                        'dayidx': (base_date + timedelta(days=random.randint(-45, 45))).strftime('%Y-%m-%d'),
                        'transaction_type': 'fraud_subtle_cycle',
                        'quarter': f"Q{((base_date.month-1)//3)+1}",
                        'is_holiday': base_date.weekday() >= 5,
                        'month_day': base_date.day,
                        'is_month_end': base_date.day >= 25
                    })
        
        return fraud_data
    
    def generate_receivable_data(self, n_records=300):
        """生成应收款项数据"""
        data = []
        
        for _ in range(n_records):
            company = random.choice(self.companies)
            vendor = random.choice([c for c in self.companies if c != company])
            
            # 虚假关联交易倾向于内部企业间往来
            is_internal = any(company in group and vendor in group for group in self.fraud_groups)
            
            data.append({
                'acntmcode': company,
                'vendorcode': vendor,
                'vendorname': self.company_names[int(vendor[3:]) - 1],
                'balancereceivable': round(np.random.lognormal(12, 1), 2),  # 四舍五入保留两位小数
                'accountdate': self._random_date(),
                'subcompany_fy': '是' if is_internal else '否'
            })
            
        return pd.DataFrame(data)
    
    def generate_payable_data(self, n_records=300):
        """生成应付款项数据"""
        data = []
        
        for _ in range(n_records):
            company = random.choice(self.companies)
            vendor = random.choice([c for c in self.companies if c != company])
            
            is_internal = any(company in group and vendor in group for group in self.fraud_groups)
            
            data.append({
                'acntmcode': company,
                'vendorcode': vendor,
                'vendorname': self.company_names[int(vendor[3:]) - 1],
                'balancereceivable': round(np.random.lognormal(12, 0.8), 2),  # 四舍五入保留两位小数
                'accountdate': self._random_date(),
                'subcompany_fy': '是' if is_internal else '否'
            })
            
        return pd.DataFrame(data)
    
    def generate_guarantee_data(self, n_records=100):
        """生成担保数据"""
        data = []
        
        for _ in range(n_records):
            # 虚假关联交易中担保关系更复杂
            if random.random() < 0.4:  # 40%概率是关联担保
                group = random.choice(self.fraud_groups)
                warrantor = random.choice(group)
                warrantee = random.choice([c for c in group if c != warrantor])
            else:
                warrantor = random.choice(self.companies)
                warrantee = random.choice([c for c in self.companies if c != warrantor])
            
            start_date = self._random_date()
            end_date = start_date + timedelta(days=random.randint(365, 1095))
            
            data.append({
                'warrantor_code': warrantor,
                'warrantee_code': warrantee,
                'zzje': round(np.random.lognormal(15, 1), 2),  # 四舍五入保留两位小数
                'zksrq': start_date.strftime('%Y-%m-%d'),
                'zjsrq': end_date.strftime('%Y-%m-%d')
            })
            
        return pd.DataFrame(data)
    
    def _random_date(self):
        """生成随机日期"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        return start_date + timedelta(days=random_days)
    
    def _get_month_end_date(self):
        """生成月末日期"""
        year = 2024
        month = random.randint(1, 12)
        day = random.randint(25, 28)  # 月末几天
        return datetime(year, month, day)
    
    def generate_truth_labels(self, settlement_df):
        """生成真实标签"""
        labels = []
        
        for _, row in settlement_df.iterrows():
            company = row['acntmcode']
            party = row['partycode']
            
            # 判断是否为虚假关联交易
            is_fraud = any(
                company in group and party in group 
                for group in self.fraud_groups
            )
            
            labels.append({
                'acntmcode': company,
                'partycode': party,
                'is_fraud': 1 if is_fraud else 0,
                'transaction_id': len(labels)
            })
            
        return pd.DataFrame(labels)
    
    def _random_date_in_range(self, start_date, end_date):
        """在指定范围内生成随机日期"""
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        return start_date + timedelta(days=random_days)
    
    def _generate_normal_amount(self):
        """生成正常交易金额（增加多样性）"""
        # 80%小额交易，15%中额交易，5%大额交易
        prob = random.random()
        if prob < 0.8:
            amount = np.random.lognormal(9, 1)    # 小额：几千到几万
        elif prob < 0.95:
            amount = np.random.lognormal(11, 0.8) # 中额：几十万
        else:
            amount = np.random.lognormal(13, 0.6) # 大额：几百万
        
        # 四舍五入保留两位小数
        return round(amount, 2)
    
    def _get_quarter_end_date(self):
        """生成季末日期"""
        quarter = random.choice(self.quarters)
        quarter_end = quarter[1]
        # 季末最后几天
        days_before_end = random.randint(0, 5)
        return quarter_end - timedelta(days=days_before_end)
    
    def _generate_distraction_transactions(self, n_distraction):
        """生成干扰交易：在欺诈企业间插入正常交易，增加检测难度"""
        distraction_data = []
        
        fraud_companies = set()
        for group in self.fraud_groups:
            fraud_companies.update(group)
        
        for _ in range(n_distraction):
            # 50%概率是欺诈企业与正常企业的交易，50%是正常企业间交易
            if random.random() < 0.5:
                from_company = random.choice(list(fraud_companies))
                to_company = random.choice([c for c in self.companies if c not in fraud_companies])
            else:
                from_company = random.choice([c for c in self.companies if c not in fraud_companies])
                to_company = random.choice([c for c in self.companies if c != from_company and c not in fraud_companies])
            
            quarter_start, quarter_end = random.choice(self.quarters)
            trade_date = self._random_date_in_range(quarter_start, quarter_end)
            
            distraction_data.append({
                'acntmcode': from_company,
                'acntmname': self.company_names[int(from_company[3:]) - 1] if int(from_company[3:]) <= len(self.company_names) else f'企业{from_company}',
                'partycode': to_company,
                'receiveunitname': self.company_names[int(to_company[3:]) - 1] if int(to_company[3:]) <= len(self.company_names) else f'企业{to_company}',
                'amount': self._generate_normal_amount(),
                'or_type_fy': random.choice(['收入', '支出', '转账']),
                'banktradedare': trade_date,
                'dayidx': trade_date.strftime('%Y-%m-%d'),
                'transaction_type': 'distraction',
                'quarter': f"Q{((trade_date.month-1)//3)+1}",
                'is_holiday': trade_date.weekday() >= 5,
                'month_day': trade_date.day,
                'is_month_end': trade_date.day >= 25
            })
        
        return distraction_data