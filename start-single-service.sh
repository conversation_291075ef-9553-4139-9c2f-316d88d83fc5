#!/bin/bash
# Startup script for running FastAPI service with integrated static file serving
# This script starts only the FastAPI service which now serves both API and static files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
API_PORT=${API_PORT:-8000}
API_HOST=${API_HOST:-0.0.0.0}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to handle cleanup on exit
cleanup() {
    print_info "Shutting down service..."
    print_info "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

print_info "Starting Enterprise Fund Flow Fraud Detection System"
print_info "=================================================="

# Check if required files exist
if [ ! -f "fraud_api.py" ]; then
    print_error "fraud_api.py not found!"
    exit 1
fi

# Check for HTML files
html_files=("fraud_detection_web.html" "fraud_detection_demo.html")
found_html=0
for file in "${html_files[@]}"; do
    if [ -f "$file" ]; then
        ((found_html++))
        print_info "Found web interface: $file"
    fi
done

if [ $found_html -eq 0 ]; then
    print_warning "No HTML interface files found"
else
    print_success "Found $found_html web interface file(s)"
fi

print_success "Starting integrated FastAPI service on port $API_PORT..."
print_info "This service provides:"
print_info "  - API endpoints for fraud detection"
print_info "  - Static file serving for web interfaces"
print_info "  - Interactive API documentation"

echo
print_success "Service starting..."
echo -e "${GREEN}=================================================="
echo -e "🚀 Integrated Service (API + Web):"
echo -e "   - API Endpoints: http://localhost:$API_PORT"
echo -e "   - API Documentation: http://localhost:$API_PORT/docs"
echo -e "   - Health Check: http://localhost:$API_PORT/health"
echo
echo -e "🌐 Web Interfaces:"
echo -e "   - Main Interface: http://localhost:$API_PORT/fraud_detection_web.html"
echo -e "   - Demo Interface: http://localhost:$API_PORT/fraud_detection_demo.html"
echo -e "   - Static Files: http://localhost:$API_PORT/static/"
echo -e "==================================================${NC}"
echo

# Start FastAPI service (this will run in foreground)
exec python fraud_api.py
