#!/bin/bash
# Startup script for running both FastAPI backend and static web server
# This script starts both services concurrently in the same container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
API_PORT=${API_PORT:-8000}
WEB_PORT=${WEB_PORT:-9000}
API_HOST=${API_HOST:-0.0.0.0}
WEB_HOST=${WEB_HOST:-0.0.0.0}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to handle cleanup on exit
cleanup() {
    print_info "Shutting down services..."
    
    # Kill background processes
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null || true
        print_info "FastAPI service stopped"
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null || true
        print_info "Web server stopped"
    fi
    
    print_info "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

print_info "Starting Enterprise Fund Flow Fraud Detection System"
print_info "=================================================="

# Check if required files exist
if [ ! -f "fraud_api.py" ]; then
    print_error "fraud_api.py not found!"
    exit 1
fi

# Start FastAPI service in background
print_info "Starting FastAPI service on port $API_PORT..."
python fraud_api.py &
API_PID=$!

# Give FastAPI a moment to start
sleep 2

# Check if FastAPI started successfully
if ! kill -0 $API_PID 2>/dev/null; then
    print_error "Failed to start FastAPI service"
    exit 1
fi

print_success "FastAPI service started (PID: $API_PID)"

# Start simple HTTP server for static files
print_info "Starting static web server on port $WEB_PORT..."

# Create a simple Python HTTP server script
cat > /tmp/simple_server.py << EOF
#!/usr/bin/env python3
import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse, parse_qs
import threading
import time

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory="/app", **kwargs)
    
    def end_headers(self):
        # Add CORS headers for cross-origin requests
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # Custom log format
        print(f"[WEB] {self.address_string()} - {format % args}")

# Start the server
PORT = int(os.environ.get('WEB_PORT', '9000'))
HOST = os.environ.get('WEB_HOST', '0.0.0.0')

try:
    with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
        print(f"[WEB] Serving static files at http://{HOST}:{PORT}/")
        print(f"[WEB] Available files:")
        for file in os.listdir('/app'):
            if file.endswith('.html'):
                print(f"[WEB]   - http://{HOST}:{PORT}/{file}")
        httpd.serve_forever()
except KeyboardInterrupt:
    print("[WEB] Server stopped")
except Exception as e:
    print(f"[WEB] Error: {e}")
    sys.exit(1)
EOF

# Start the web server in background
python /tmp/simple_server.py &
WEB_PID=$!

# Give web server a moment to start
sleep 2

# Check if web server started successfully
if ! kill -0 $WEB_PID 2>/dev/null; then
    print_error "Failed to start web server"
    cleanup
    exit 1
fi

print_success "Static web server started (PID: $WEB_PID)"

# Print service information
echo
print_success "All services are running!"
echo -e "${GREEN}=================================================="
echo -e "🚀 FastAPI Service:"
echo -e "   - API Endpoints: http://localhost:$API_PORT"
echo -e "   - API Documentation: http://localhost:$API_PORT/docs"
echo -e "   - Health Check: http://localhost:$API_PORT/health"
echo
echo -e "🌐 Web Interface:"
echo -e "   - Static Files: http://localhost:$WEB_PORT"
echo -e "   - Main Interface: http://localhost:$WEB_PORT/fraud_detection_web.html"
echo -e "   - Demo Interface: http://localhost:$WEB_PORT/fraud_detection_demo.html"
echo -e "==================================================${NC}"
echo

# Wait for both processes and monitor them
while true; do
    # Check if FastAPI is still running
    if ! kill -0 $API_PID 2>/dev/null; then
        print_error "FastAPI service died unexpectedly"
        cleanup
        exit 1
    fi
    
    # Check if web server is still running
    if ! kill -0 $WEB_PID 2>/dev/null; then
        print_error "Web server died unexpectedly"
        cleanup
        exit 1
    fi
    
    # Sleep for a bit before checking again
    sleep 5
done
