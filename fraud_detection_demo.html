<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欺诈检测系统 - 离线演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }

        .content {
            padding: 30px;
        }

        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 500;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .demo-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .demo-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }

        .demo-card.normal {
            border-color: #28a745;
            background: #f8fff9;
        }

        .demo-card.suspicious {
            border-color: #ffc107;
            background: #fffdf5;
        }

        .demo-card.high-risk {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .demo-card h3 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .demo-features {
            margin-bottom: 15px;
        }

        .demo-features .feature {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .demo-features .label {
            color: #6c757d;
        }

        .demo-features .value {
            font-weight: bold;
            color: #2c3e50;
        }

        .demo-result {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .result-normal {
            background: #d4edda;
            color: #155724;
        }

        .result-suspicious {
            background: #fff3cd;
            color: #856404;
        }

        .result-high-risk {
            background: #f8d7da;
            color: #721c24;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            display: inline-block;
            text-decoration: none;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .model-info {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .model-info h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .model-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }

        .feature-item .icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-item .title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .feature-item .desc {
            font-size: 14px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 企业资金流向欺诈检测系统</h1>
            <p>基于机器学习的智能风险识别平台 - 离线演示版</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator"></div>
                <span>🟢 离线演示模式</span>
            </div>
            <div class="status-item">
                <span>📊 模型版本: <strong>XGBoost v1.0</strong></span>
            </div>
            <div class="status-item">
                <span>🕒 <span id="currentTime"></span></span>
            </div>
        </div>

        <div class="content">
            <!-- 模型信息 -->
            <div class="demo-section">
                <h2>🤖 模型特性介绍</h2>
                <div class="model-info">
                    <h3>核心检测能力</h3>
                    <div class="model-features">
                        <div class="feature-item">
                            <div class="icon">💰</div>
                            <div class="title">交易模式分析</div>
                            <div class="desc">识别异常的交易金额、频率和时间模式</div>
                        </div>
                        <div class="feature-item">
                            <div class="icon">🔄</div>
                            <div class="title">循环交易检测</div>
                            <div class="desc">发现企业间的资金循环流转</div>
                        </div>
                        <div class="feature-item">
                            <div class="icon">📅</div>
                            <div class="title">时间异常分析</div>
                            <div class="desc">检测季末、月末集中交易行为</div>
                        </div>
                        <div class="feature-item">
                            <div class="icon">📊</div>
                            <div class="title">风险量化评估</div>
                            <div class="desc">提供0-100%的精确风险概率</div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <a href="fraud_detection_web.html" class="btn btn-primary">
                        🚀 启动完整版系统 (需要API服务)
                    </a>
                </div>
            </div>

            <!-- 演示案例 -->
            <div class="demo-section">
                <h2>📋 典型案例演示</h2>
                <div class="demo-grid">
                    <!-- 正常企业 -->
                    <div class="demo-card normal" onclick="showDetail('normal')">
                        <h3>✅ 正常企业 - COM0001</h3>
                        <div class="demo-features">
                            <div class="feature">
                                <span class="label">总交易次数:</span>
                                <span class="value">45次</span>
                            </div>
                            <div class="feature">
                                <span class="label">平均交易金额:</span>
                                <span class="value">52,222元</span>
                            </div>
                            <div class="feature">
                                <span class="label">交易对手数:</span>
                                <span class="value">12家</span>
                            </div>
                            <div class="feature">
                                <span class="label">季末交易比例:</span>
                                <span class="value">8.5%</span>
                            </div>
                            <div class="feature">
                                <span class="label">循环交易对手:</span>
                                <span class="value">0家</span>
                            </div>
                        </div>
                        <div class="demo-result result-normal">
                            🟢 风险概率: 12.5% - 低风险
                        </div>
                    </div>

                    <!-- 可疑企业 -->
                    <div class="demo-card suspicious" onclick="showDetail('suspicious')">
                        <h3>⚠️ 可疑企业 - COM0005</h3>
                        <div class="demo-features">
                            <div class="feature">
                                <span class="label">总交易次数:</span>
                                <span class="value">28次</span>
                            </div>
                            <div class="feature">
                                <span class="label">平均交易金额:</span>
                                <span class="value">196,429元</span>
                            </div>
                            <div class="feature">
                                <span class="label">交易对手数:</span>
                                <span class="value">5家</span>
                            </div>
                            <div class="feature">
                                <span class="label">季末交易比例:</span>
                                <span class="value">45.2%</span>
                            </div>
                            <div class="feature">
                                <span class="label">循环交易对手:</span>
                                <span class="value">3家</span>
                            </div>
                        </div>
                        <div class="demo-result result-suspicious">
                            🟡 风险概率: 68.3% - 中等风险
                        </div>
                    </div>

                    <!-- 高风险企业 -->
                    <div class="demo-card high-risk" onclick="showDetail('high-risk')">
                        <h3>🚨 高风险企业 - COM0010</h3>
                        <div class="demo-features">
                            <div class="feature">
                                <span class="label">总交易次数:</span>
                                <span class="value">15次</span>
                            </div>
                            <div class="feature">
                                <span class="label">平均交易金额:</span>
                                <span class="value">666,667元</span>
                            </div>
                            <div class="feature">
                                <span class="label">交易对手数:</span>
                                <span class="value">3家</span>
                            </div>
                            <div class="feature">
                                <span class="label">季末交易比例:</span>
                                <span class="value">80.0%</span>
                            </div>
                            <div class="feature">
                                <span class="label">循环交易对手:</span>
                                <span class="value">3家</span>
                            </div>
                        </div>
                        <div class="demo-result result-high-risk">
                            🔴 风险概率: 89.7% - 高风险
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细分析结果 -->
            <div class="demo-section" id="detailSection" style="display: none;">
                <h2>🔍 详细风险分析</h2>
                <div id="detailContent">
                    <!-- 详细内容将在这里动态填充 -->
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="demo-section">
                <h2>📖 系统说明</h2>
                <div style="line-height: 1.8; color: #495057;">
                    <h3 style="margin-bottom: 15px;">🎯 检测原理</h3>
                    <p style="margin-bottom: 15px;">
                        本系统基于XGBoost机器学习算法，通过分析企业的历史交易数据，识别出17个关键特征指标，
                        包括交易金额分布、交易对手关系、时间模式、应收应付款情况等，构建智能风险评估模型。
                    </p>
                    
                    <h3 style="margin-bottom: 15px;">🔍 主要检测指标</h3>
                    <ul style="margin: 0 0 15px 20px;">
                        <li><strong>交易金额异常:</strong> 识别异常大额、整数金额等可疑交易</li>
                        <li><strong>时间集中度:</strong> 检测季末、月末的集中交易行为</li>
                        <li><strong>循环交易:</strong> 发现企业间的资金循环流转模式</li>
                        <li><strong>交易对手分析:</strong> 评估交易对手的集中度和关联性</li>
                        <li><strong>财务指标异常:</strong> 结合应收应付、担保等财务数据</li>
                    </ul>
                    
                    <h3 style="margin-bottom: 15px;">📊 风险等级说明</h3>
                    <ul style="margin: 0 0 15px 20px;">
                        <li><strong>低风险 (0-30%):</strong> 交易模式正常，可继续业务往来</li>
                        <li><strong>中等风险 (30-70%):</strong> 存在异常迹象，建议加强监控</li>
                        <li><strong>高风险 (70-100%):</strong> 高度可疑，建议暂停业务并深入调查</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
        });

        // 显示详细分析
        function showDetail(type) {
            const detailSection = document.getElementById('detailSection');
            const detailContent = document.getElementById('detailContent');
            
            let content = '';
            
            switch(type) {
                case 'normal':
                    content = `
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                            <h3 style="color: #155724; margin-bottom: 15px;">✅ 正常企业分析报告</h3>
                            <div style="color: #155724; line-height: 1.6;">
                                <p><strong>企业代码:</strong> COM0001</p>
                                <p><strong>风险概率:</strong> 12.5%</p>
                                <p><strong>风险等级:</strong> 低风险</p>
                                <p><strong>预测标签:</strong> 正常</p>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4 style="margin-bottom: 15px;">🔍 风险因素分析</h4>
                            <ul style="line-height: 1.6; margin-left: 20px;">
                                <li>✅ 交易金额分布正常，无异常大额交易</li>
                                <li>✅ 交易对手数量合理(12家)，未过度集中</li>
                                <li>✅ 季末交易比例较低(8.5%)，无集中交易嫌疑</li>
                                <li>✅ 无循环交易对手，交易关系清晰</li>
                                <li>✅ 应收应付款比例合理，财务状况健康</li>
                            </ul>
                            <div style="margin-top: 15px; padding: 15px; background: #e9ecef; border-radius: 5px;">
                                <strong>建议:</strong> 该企业风险较低，可以继续正常业务往来，保持常规监控即可。
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'suspicious':
                    content = `
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                            <h3 style="color: #856404; margin-bottom: 15px;">⚠️ 可疑企业分析报告</h3>
                            <div style="color: #856404; line-height: 1.6;">
                                <p><strong>企业代码:</strong> COM0005</p>
                                <p><strong>风险概率:</strong> 68.3%</p>
                                <p><strong>风险等级:</strong> 中等风险</p>
                                <p><strong>预测标签:</strong> 可疑</p>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4 style="margin-bottom: 15px;">🔍 风险因素分析</h4>
                            <ul style="line-height: 1.6; margin-left: 20px;">
                                <li>⚠️ 平均交易金额较高(196,429元)，存在异常</li>
                                <li>⚠️ 交易对手数量较少(5家)，过度集中</li>
                                <li>🚨 季末交易比例高达45.2%，涉嫌季末调节</li>
                                <li>🚨 存在3家循环交易对手，可能存在资金循环</li>
                                <li>⚠️ 应收应付款金额较高，存在关联交易嫌疑</li>
                            </ul>
                            <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 5px;">
                                <strong>建议:</strong> 该企业存在一定风险，建议加强监控，进行进一步的人工审核，特别关注其季末交易和循环交易情况。
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'high-risk':
                    content = `
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                            <h3 style="color: #721c24; margin-bottom: 15px;">🚨 高风险企业分析报告</h3>
                            <div style="color: #721c24; line-height: 1.6;">
                                <p><strong>企业代码:</strong> COM0010</p>
                                <p><strong>风险概率:</strong> 89.7%</p>
                                <p><strong>风险等级:</strong> 高风险</p>
                                <p><strong>预测标签:</strong> 欺诈</p>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4 style="margin-bottom: 15px;">🔍 风险因素分析</h4>
                            <ul style="line-height: 1.6; margin-left: 20px;">
                                <li>🚨 平均交易金额异常高(666,667元)，疑似人为操控</li>
                                <li>🚨 交易对手极度集中(仅3家)，存在关联交易嫌疑</li>
                                <li>🚨 季末交易比例高达80%，明显存在季末调节行为</li>
                                <li>🚨 循环交易对手数量与总对手数相等，资金循环明显</li>
                                <li>🚨 内部交易比例高达95%，几乎全为关联交易</li>
                                <li>🚨 担保金额巨大(500万)，存在财务风险</li>
                            </ul>
                            <div style="margin-top: 15px; padding: 15px; background: #f8d7da; border-radius: 5px;">
                                <strong>紧急建议:</strong> 该企业风险极高，强烈建议立即暂停相关业务往来，启动详细调查程序。交易模式存在明显的人为操控迹象，涉嫌虚假关联交易。
                            </div>
                        </div>
                    `;
                    break;
            }
            
            detailContent.innerHTML = content;
            detailSection.style.display = 'block';
            
            // 滚动到详细分析区域
            detailSection.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
