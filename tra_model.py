import pandas as pd
from datetime import timedelta
import random
import pickle
import joblib
import os
from typing import Dict, Any

# 尝试导入XGBoost，如果失败则使用替代方案
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
    print("✓ XGBoost已成功导入")
except ImportError as e:
    print(f"⚠️ XGBoost导入失败: {e}")
    print("将使用sklearn的RandomForest作为替代")
    from sklearn.ensemble import RandomForestClassifier
    XGBOOST_AVAILABLE = False

class MVPFraudDetector:
    def __init__(self):
        self.model = None
        self.feature_columns = None
        self.model_metadata = None
        
    def create_mvp_features(self, settlement_df, receivable_df, payable_df, guarantee_df):
        """创建MVP版本的特征 - 改进版本"""
        features = []
        
        # 按企业编码分组计算特征
        for company in settlement_df['acntmcode'].unique():
            company_features = {'company_code': company}
            
            # 该企业的所有交易
            company_txns = settlement_df[settlement_df['acntmcode'] == company].copy()
            
            # 基础交易特征
            company_features.update({
                'total_transactions': len(company_txns),
                'total_amount': company_txns['amount'].sum(),
                'avg_amount': company_txns['amount'].mean(),
                'max_amount': company_txns['amount'].max(),
                'min_amount': company_txns['amount'].min(),
                'amount_std': company_txns['amount'].std(),
                'unique_partners': company_txns['partycode'].nunique(),
            })
            
            # 时间相关特征
            company_txns['day'] = pd.to_datetime(company_txns['banktradedare']).dt.day
            company_txns['weekday'] = pd.to_datetime(company_txns['banktradedare']).dt.weekday
            company_txns['month'] = pd.to_datetime(company_txns['banktradedare']).dt.month
            
            # 季末集中度特征 (关键指标)
            quarter_end_days = [30, 31, 90, 91, 180, 181, 365, 366]  # 各季度末几天
            quarter_end_txns = company_txns[
                (company_txns['day'] >= 28) |  # 月末
                (company_txns['month'].isin([3, 6, 9, 12]) & (company_txns['day'] >= 25))  # 季末
            ]
            company_features['quarter_end_ratio'] = len(quarter_end_txns) / max(len(company_txns), 1)
            
            # 工作日vs周末比例
            weekend_txns = company_txns[company_txns['weekday'] >= 5]
            company_features['weekend_ratio'] = len(weekend_txns) / max(len(company_txns), 1)
            
            # 循环交易特征 (改进)
            partners = company_txns['partycode'].unique()
            circular_partners = 0
            circular_amount = 0
            
            for partner in partners:
                # 检查是否存在反向交易
                reverse_txns = settlement_df[
                    (settlement_df['acntmcode'] == partner) & 
                    (settlement_df['partycode'] == company)
                ]
                if len(reverse_txns) > 0:
                    circular_partners += 1
                    # 计算循环金额相似度
                    out_amounts = company_txns[company_txns['partycode'] == partner]['amount']
                    in_amounts = reverse_txns['amount']
                    if len(out_amounts) > 0 and len(in_amounts) > 0:
                        amount_similarity = abs(out_amounts.mean() - in_amounts.mean()) / max(out_amounts.mean(), in_amounts.mean())
                        if amount_similarity < 0.2:  # 金额相似度高
                            circular_amount += out_amounts.sum()
            
            company_features['circular_partner_ratio'] = circular_partners / max(len(partners), 1)
            company_features['circular_amount_ratio'] = circular_amount / max(company_features['total_amount'], 1)
            
            # 快速往返特征 (改进)
            quick_returns = 0
            quick_return_amount = 0
            
            for _, txn in company_txns.iterrows():
                txn_date = pd.to_datetime(txn['banktradedare'])
                partner = txn['partycode']
                
                # 查找14天内的反向交易 (扩大窗口)
                window_start = txn_date
                window_end = txn_date + timedelta(days=14)
                
                reverse_in_window = settlement_df[
                    (settlement_df['acntmcode'] == partner) &
                    (settlement_df['partycode'] == company) &
                    (pd.to_datetime(settlement_df['banktradedare']) >= window_start) &
                    (pd.to_datetime(settlement_df['banktradedare']) <= window_end)
                ]
                
                if len(reverse_in_window) > 0:
                    quick_returns += 1
                    quick_return_amount += txn['amount']
            
            company_features['quick_return_ratio'] = quick_returns / max(len(company_txns), 1)
            company_features['quick_return_amount_ratio'] = quick_return_amount / max(company_features['total_amount'], 1)
            
            # 金额模式特征
            company_features['amount_concentration'] = company_features['max_amount'] / max(company_features['avg_amount'], 1)
            company_features['amount_variance_coeff'] = company_features['amount_std'] / max(company_features['avg_amount'], 1)
            
            # 交易频率特征
            if len(company_txns) > 1:
                dates = pd.to_datetime(company_txns['banktradedare']).sort_values()
                date_diffs = dates.diff().dt.days.dropna()
                company_features['avg_interval_days'] = date_diffs.mean() if len(date_diffs) > 0 else 0
                company_features['min_interval_days'] = date_diffs.min() if len(date_diffs) > 0 else 0
            else:
                company_features['avg_interval_days'] = 0
                company_features['min_interval_days'] = 0
            
            # 内部交易比例
            internal_receivables = receivable_df[
                (receivable_df['acntmcode'] == company) & 
                (receivable_df['subcompany_fy'] == '是')
            ]
            internal_payables = payable_df[
                (payable_df['acntmcode'] == company) & 
                (payable_df['subcompany_fy'] == '是')
            ]
            
            total_receivables = len(receivable_df[receivable_df['acntmcode'] == company])
            total_payables = len(payable_df[payable_df['acntmcode'] == company])
            
            company_features['internal_receivable_ratio'] = len(internal_receivables) / max(total_receivables, 1)
            company_features['internal_payable_ratio'] = len(internal_payables) / max(total_payables, 1)
            
            # 担保关系特征
            as_warrantor = guarantee_df[guarantee_df['warrantor_code'] == company]
            as_warrantee = guarantee_df[guarantee_df['warrantee_code'] == company]
            
            company_features['guarantee_out_count'] = len(as_warrantor)
            company_features['guarantee_in_count'] = len(as_warrantee)
            company_features['guarantee_total_amount'] = as_warrantor['zzje'].sum() if len(as_warrantor) > 0 else 0
            
            features.append(company_features)
        
        return pd.DataFrame(features)
    
    def train_mvp_model(self, features_df, labels_df, settlement_df):
        """训练MVP模型 - 改进版本（时间序列分割）"""
        # 合并特征和标签
        company_labels = labels_df.groupby('acntmcode')['is_fraud'].max().reset_index()
        company_labels.columns = ['company_code', 'is_fraud']
        
        training_data = features_df.merge(company_labels, on='company_code', how='left')
        training_data['is_fraud'] = training_data['is_fraud'].fillna(0)
        
        # 准备训练数据
        feature_cols = [col for col in training_data.columns if col not in ['company_code', 'is_fraud']]
        X = training_data[feature_cols].fillna(0)
        y = training_data['is_fraud']
        
        print(f"训练样本: {len(X)}")
        print(f"欺诈样本比例: {y.mean():.3f}")
        print(f"特征数量: {len(feature_cols)}")
        
        # 时间序列分割 - 使用Q1-Q3训练，Q4测试
        train_companies = []
        test_companies = []
        
        for company in training_data['company_code']:
            company_txns = settlement_df[settlement_df['acntmcode'] == company]
            if len(company_txns) > 0:
                # 检查公司是否有Q4的交易
                if 'quarter' in company_txns.columns:
                    q4_txns = company_txns[company_txns['quarter'] == 'Q4']
                    q123_txns = company_txns[company_txns['quarter'].isin(['Q1', 'Q2', 'Q3'])]
                    
                    # 如果同时有Q1-Q3和Q4的交易，优先放入训练集
                    if len(q123_txns) > 0 and len(q4_txns) > 0:
                        if random.random() < 0.7:  # 70%概率放入训练集
                            train_companies.append(company)
                        else:
                            test_companies.append(company)
                    elif len(q123_txns) > 0:
                        train_companies.append(company)
                    elif len(q4_txns) > 0:
                        test_companies.append(company)
                    else:
                        train_companies.append(company)  # 默认放入训练集
                else:
                    train_companies.append(company)  # 没有季度信息的放入训练集
        
        # 确保训练集和测试集都有足够的样本
        if len(train_companies) < 20 or len(test_companies) < 10:
            print("时间分割样本不足，使用随机分割...")
            from sklearn.model_selection import train_test_split
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y if len(y.unique()) > 1 else None)
            train_idx = X_train.index
            test_idx = X_test.index
        else:
            print(f"使用时间分割: 训练({len(train_companies)}家) vs 测试({len(test_companies)}家)")
            train_idx = training_data[training_data['company_code'].isin(train_companies)].index
            test_idx = training_data[training_data['company_code'].isin(test_companies)].index
            
            X_train, X_test = X.loc[train_idx], X.loc[test_idx]
            y_train, y_test = y.loc[train_idx], y.loc[test_idx]
        
        # 处理类别不平衡
        if len(y_train) > 0:
            fraud_ratio = y_train.sum() / len(y_train)
            if fraud_ratio > 0:
                scale_pos_weight = (len(y_train) - y_train.sum()) / max(y_train.sum(), 1)
            else:
                scale_pos_weight = 1
        else:
            fraud_ratio = 0
            scale_pos_weight = 1
        
        print(f"训练集欺诈比例: {fraud_ratio:.3f}")
        print(f"类别权重: {scale_pos_weight:.2f}")
        
        # 训练模型 - 支持XGBoost和RandomForest
        if XGBOOST_AVAILABLE:
            print("使用XGBoost训练模型...")
            import xgboost as xgb
            self.model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        else:
            print("使用RandomForest训练模型...")
            from sklearn.ensemble import RandomForestClassifier
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42
            )
        
        # 训练模型（移除early_stopping_rounds参数）
        self.model.fit(X_train, y_train)
        
        # 评估
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]
        
        from sklearn.metrics import classification_report, roc_auc_score, confusion_matrix
        
        # 安全计算AUC
        try:
            if len(y_test.unique()) > 1:
                auc_score = roc_auc_score(y_test, y_pred_proba)
            else:
                auc_score = 0.5  # 只有一个类别时AUC无意义
        except:
            auc_score = 0.5
        
        print("\n=== MVP模型评估 ===")
        print(f"AUC: {auc_score:.4f}")
        
        # 安全打印混淆矩阵
        try:
            print(f"混淆矩阵:")
            print(confusion_matrix(y_test, y_pred))
        except:
            print("无法计算混淆矩阵")
        
        # 安全打印分类报告
        try:
            print("\n分类报告:")
            print(classification_report(y_test, y_pred, zero_division=0))
        except:
            print("无法生成分类报告")
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n=== 特征重要性 (Top 10) ===")
        print(feature_importance.head(10))
        
        # 保存模型和元数据
        self.feature_columns = feature_cols
        self.model_metadata = {
            'auc': float(auc_score),  # 确保是Python float
            'feature_importance': feature_importance.to_dict('records'),  # 转换为可序列化的字典列表
            'train_samples': int(len(X_train)),  # 确保是Python int
            'test_samples': int(len(X_test)),  # 确保是Python int
            'fraud_ratio': float(fraud_ratio),  # 确保是Python float
            'feature_columns': list(feature_cols)  # 确保是Python list
        }
        
        self.save_model()
        
        return {
            'model': self.model,
            'auc': auc_score,
            'feature_importance': feature_importance,
            'test_predictions': pd.DataFrame({
                'company_code': training_data.loc[test_idx, 'company_code'],
                'true_label': y_test,
                'predicted_proba': y_pred_proba,
                'predicted_label': y_pred
            }),
            'train_test_split': {'train_companies': len(train_companies), 'test_companies': len(test_companies)}
        }
    
    def save_model(self, model_dir='models'):
        """保存模型和元数据"""
        try:
            # 创建模型目录
            os.makedirs(model_dir, exist_ok=True)
            
            # 保存模型
            model_path = os.path.join(model_dir, 'fraud_detection_model.pkl')
            joblib.dump(self.model, model_path)
            
            # 保存特征列信息
            features_path = os.path.join(model_dir, 'feature_columns.pkl')
            joblib.dump(self.feature_columns, features_path)
            
            # 保存元数据
            metadata_path = os.path.join(model_dir, 'model_metadata.pkl')
            joblib.dump(self.model_metadata, metadata_path)
            
            print(f"✓ 模型已保存到 {model_dir} 目录")
            print(f"  - 模型文件: {model_path}")
            print(f"  - 特征信息: {features_path}")
            print(f"  - 元数据: {metadata_path}")
            
        except Exception as e:
            print(f"❌ 模型保存失败: {e}")
    
    def load_model(self, model_dir='models'):
        """加载保存的模型"""
        try:
            # 加载模型
            model_path = os.path.join(model_dir, 'fraud_detection_model.pkl')
            self.model = joblib.load(model_path)
            
            # 加载特征列信息
            features_path = os.path.join(model_dir, 'feature_columns.pkl')
            self.feature_columns = joblib.load(features_path)
            
            # 加载元数据
            metadata_path = os.path.join(model_dir, 'model_metadata.pkl')
            self.model_metadata = joblib.load(metadata_path)
            
            print(f"✓ 模型已加载")
            print(f"  - AUC: {self.model_metadata['auc']:.4f}")
            print(f"  - 特征数量: {len(self.feature_columns)}")
            print(f"  - 训练样本: {self.model_metadata['train_samples']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def predict_fraud_probability(self, features_dict: Dict[str, Any]) -> Dict[str, Any]:
        """预测单个企业的欺诈概率"""
        if self.model is None:
            raise ValueError("模型未训练或未加载，请先训练模型或加载已保存的模型")
        
        if self.feature_columns is None:
            raise ValueError("特征列信息缺失，请确保模型正确加载")
        
        try:
            # 构建特征向量
            feature_vector = []
            for col in self.feature_columns:
                value = features_dict.get(col, 0)  # 缺失特征用0填充
                feature_vector.append(value)
            
            # 转换为DataFrame
            import numpy as np
            X = np.array(feature_vector).reshape(1, -1)
            
            # 预测
            pred_proba = self.model.predict_proba(X)[0, 1]
            pred_label = self.model.predict(X)[0]
            
            # 风险等级
            if pred_proba >= 0.8:
                risk_level = "极高"
            elif pred_proba >= 0.6:
                risk_level = "高"
            elif pred_proba >= 0.4:
                risk_level = "中"
            elif pred_proba >= 0.2:
                risk_level = "低"
            else:
                risk_level = "极低"
            
            return {
                'fraud_probability': round(pred_proba, 4),
                'predicted_label': int(pred_label),
                'risk_level': risk_level,
                'is_suspicious': pred_proba >= 0.5
            }
            
        except Exception as e:
            raise ValueError(f"预测失败: {e}")
    
    def predict_companies_batch(self, companies_features: pd.DataFrame) -> pd.DataFrame:
        """批量预测多个企业的欺诈概率"""
        if self.model is None:
            raise ValueError("模型未训练或未加载")
        
        try:
            # 确保特征列顺序正确
            X = companies_features[self.feature_columns].fillna(0)
            
            # 预测
            pred_proba = self.model.predict_proba(X)[:, 1]
            pred_labels = self.model.predict(X)
            
            # 构建结果DataFrame
            results = companies_features.copy()
            results['fraud_probability'] = pred_proba
            results['predicted_label'] = pred_labels
            results['risk_level'] = pd.cut(pred_proba, 
                                         bins=[0, 0.2, 0.4, 0.6, 0.8, 1.0], 
                                         labels=['极低', '低', '中', '高', '极高'])
            results['is_suspicious'] = pred_proba >= 0.5
            
            return results
            
        except Exception as e:
            raise ValueError(f"批量预测失败: {e}")