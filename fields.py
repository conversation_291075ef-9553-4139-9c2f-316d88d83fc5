# MVP核心字段定义
MVP_FIELDS = {
    # 资金结算表 - 核心交易数据
    'settlement': [
        'acntmcode',         # 本方单位编码 - 主体标识
        'acntmname',         # 本方单位名称
        'partycode',         # 对方单位编码 - 关联方标识
        'receiveunitname',   # 对方单位名称
        'amount',            # 交易金额 - 核心指标
        'or_type_fy',        # 收支标记 - 资金流向
        'banktradedare',     # 交易时间 - 时间模式分析
        'dayidx'             # 数据日期
    ],
    
    # 应收款项表 - 债权关系
    'receivable': [
        'acntmcode',         # 本方单位编码
        'vendorcode',        # 客商编码
        'vendorname',        # 客商名称
        'balancereceivable', # 款项余额
        'accountdate',       # 入账日期
        'subcompany_fy'      # 是否内部企业
    ],
    
    # 应付款项表 - 债务关系
    'payable': [
        'acntmcode',         # 本方单位编码
        'vendorcode',        # 客商编码
        'vendorname',        # 客商名称
        'balancereceivable', # 款项余额
        'accountdate',       # 入账日期
        'subcompany_fy'      # 是否内部企业
    ],
    
    # 担保表 - 担保关系
    'guarantee': [
        'warrantor_code',    # 担保人编码
        'warrantee_code',    # 被担保人编码
        'zzje',              # 担保总金额
        'zksrq',             # 担保起始日期
        'zjsrq'              # 担保终止日期
    ]
}