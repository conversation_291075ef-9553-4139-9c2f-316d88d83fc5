#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业资金流向欺诈检测系统 - 启动脚本
Enterprise Fund Flow Fraud Detection System - Startup Script
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path

def print_header():
    print("=" * 50)
    print("  企业资金流向欺诈检测系统")
    print("  Enterprise Fund Flow Fraud Detection System")
    print("=" * 50)
    print()

def print_menu():
    print("请选择启动模式 / Please select startup mode:")
    print("1. 启动API + 打开网页 (Start API + Open Web)")
    print("2. 仅启动API服务 (API Service Only)")
    print("3. 仅打开演示页面 (Demo Pages Only)")
    print("4. 训练模型 + 启动服务 (Train Model + Start Service)")
    print("5. 退出 (Exit)")
    print()

def start_api_service():
    """启动API服务"""
    try:
        # 启动fraud_api.py
        process = subprocess.Popen([sys.executable, "fraud_api.py"])
        return process
    except Exception as e:
        print(f"启动API服务失败: {e}")
        return None

def open_web_pages():
    """打开网页界面"""
    try:
        # 获取当前目录的绝对路径
        current_dir = Path(__file__).parent.absolute()
        
        # 打开网页文件
        web_file = current_dir / "fraud_detection_web.html"
        demo_file = current_dir / "fraud_detection_demo.html"
        
        if web_file.exists():
            webbrowser.open(f"file://{web_file}")
            print(f"已打开完整版界面: {web_file}")
        
        if demo_file.exists():
            webbrowser.open(f"file://{demo_file}")
            print(f"已打开演示版界面: {demo_file}")
            
    except Exception as e:
        print(f"打开网页失败: {e}")

def train_model():
    """训练模型"""
    try:
        print("开始训练模型...")
        result = subprocess.run([sys.executable, "whole.py", "1"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("模型训练完成！")
            return True
        else:
            print(f"模型训练失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"训练模型时出错: {e}")
        return False

def main():
    print_header()
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选择 (1-5) / Enter choice (1-5): ").strip()
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            sys.exit(0)
        
        if choice == "1":
            print("\n正在启动API服务...")
            print("Starting API service...")
            
            # 启动API服务
            api_process = start_api_service()
            if api_process:
                print("API服务启动中，等待3秒...")
                time.sleep(3)
                
                print("正在打开网页界面...")
                open_web_pages()
                
                print("\n系统已启动！")
                print("API文档: http://localhost:8000/docs")
                print("健康检查: http://localhost:8000/health")
                print("\n按Enter键退出...")
                input()
            break
            
        elif choice == "2":
            print("\n正在启动API服务...")
            print("Starting API service...")
            
            # 直接运行API服务（前台）
            try:
                subprocess.run([sys.executable, "fraud_api.py"])
            except KeyboardInterrupt:
                print("\nAPI服务已停止")
            break
            
        elif choice == "3":
            print("\n正在打开演示页面...")
            print("Opening demo pages...")
            
            open_web_pages()
            
            print("\n演示页面已打开！")
            print("如需完整功能，请选择选项1启动API服务")
            print("For full functionality, choose option 1 to start API service")
            print("\n按Enter键退出...")
            input()
            break
            
        elif choice == "4":
            print("\n开始训练模型...")
            print("Training model...")
            
            if train_model():
                print("\n模型训练完成，启动API服务...")
                api_process = start_api_service()
                
                if api_process:
                    print("等待API服务启动...")
                    time.sleep(3)
                    
                    print("正在打开网页界面...")
                    open_web_pages()
                    
                    print("\n系统完全启动！")
                    print("按Enter键退出...")
                    input()
            else:
                print("模型训练失败，请检查错误信息")
                input("按Enter键继续...")
                continue
            break
            
        elif choice == "5":
            print("\n感谢使用！")
            print("Thank you!")
            break
            
        else:
            print("\n无效选择，请重新输入")
            print("Invalid choice, please try again")
            print()
            continue

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        input("按Enter键退出...")
    finally:
        print("\n程序已退出")
