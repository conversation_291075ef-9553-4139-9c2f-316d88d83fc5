@echo off
echo ========================================
echo   Fraud Detection System Launcher
echo ========================================
echo.

echo Please select:
echo 1. Start API + Open Web
echo 2. Start API only  
echo 3. Open Demo only
echo 4. Train + Start
echo 5. Exit
echo.

set /p choice=Enter choice (1-5): 

if "%choice%"=="1" goto option1
if "%choice%"=="2" goto option2
if "%choice%"=="3" goto option3
if "%choice%"=="4" goto option4
if "%choice%"=="5" goto option5
goto invalid

:option1
echo.
echo Starting API service...
start /b python fraud_api.py
timeout /t 3 /nobreak >nul
echo Opening web pages...
start fraud_detection_web.html
start fraud_detection_demo.html
echo.
echo System started!
echo API Docs: http://localhost:8000/docs
echo Health: http://localhost:8000/health
echo.
pause
goto end

:option2
echo.
echo Starting API service...
python fraud_api.py
goto end

:option3
echo.
echo Opening demo pages...
start fraud_detection_demo.html
echo Demo opened!
echo.
echo To start API service, run: python fraud_api.py
echo.
pause
goto end

:option4
echo.
echo Training model...
python whole.py 1
echo.
echo Starting API service...
start /b python fraud_api.py
timeout /t 3 /nobreak >nul
start fraud_detection_web.html
echo System ready!
pause
goto end

:option5
echo.
echo Thank you!
goto end

:invalid
echo.
echo Invalid choice!
pause
goto end

:end
echo.
echo Done.
