# Environment Configuration for Enterprise Fund Flow Fraud Detection System
# Copy this file to .env and update with your actual values

# =============================================================================
# LLM Configuration
# =============================================================================
# SiliconFlow API Token (required for LLM-enhanced features)
SILICONFLOW_API_TOKEN=your-api-token-here

# SiliconFlow API URL (default is usually fine)
SILICONFLOW_API_URL=https://api.siliconflow.cn/v1/chat/completions

# Model to use for LLM features
SILICONFLOW_MODEL=Qwen/QwQ-32B

# =============================================================================
# Data Generation Parameters
# =============================================================================
# Number of records to generate per batch
DEFAULT_BATCH_SIZE=20

# Total number of records to generate
DEFAULT_TOTAL_SIZE=2000

# Fraud ratio (percentage of fraudulent transactions)
DEFAULT_FRAUD_RATIO=0.05

# =============================================================================
# Application Configuration
# =============================================================================
# API server host (0.0.0.0 for Docker, localhost for local dev)
API_HOST=0.0.0.0

# API server port
API_PORT=8000

# Web server port (when using web_server.py)
WEB_PORT=9000

# =============================================================================
# Model Configuration
# =============================================================================
# Minimum transaction amount
MIN_AMOUNT=10000

# Maximum transaction amount
MAX_AMOUNT=5000000

# Number of companies in simulation
COMPANY_COUNT=100

# =============================================================================
# Development Settings
# =============================================================================
# Enable debug mode (true/false)
DEBUG=false

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Enable verbose output
VERBOSE_OUTPUT=true

# =============================================================================
# Docker Configuration
# =============================================================================
# Docker image tag to use
DOCKER_IMAGE_TAG=latest

# Docker registry (leave empty for local builds)
DOCKER_REGISTRY=

# =============================================================================
# Usage Instructions
# =============================================================================
# 1. Copy this file: cp .env.example .env
# 2. Edit .env with your actual values
# 3. Run with Docker Compose: docker-compose up
# 4. Or source in shell: source .env && python fraud_api.py
