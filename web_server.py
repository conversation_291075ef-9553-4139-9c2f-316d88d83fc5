#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务器 - 提供静态文件服务和API代理
解决CORS问题的完整解决方案
"""

import os
import sys
import asyncio
import webbrowser
from pathlib import Path
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入API应用
try:
    from fraud_api import app as api_app
    print("✓ 成功导入API应用")
except ImportError as e:
    print(f"❌ 导入API应用失败: {e}")
    sys.exit(1)

# 手动初始化模型（解决挂载后startup事件不触发的问题）
def init_model():
    """手动初始化模型"""
    try:
        print("🔧 正在初始化模型...")
        
        # 导入必要的模块
        from tra_model import MVPFraudDetector
        import fraud_api
        
        # 创建检测器实例
        fraud_api.detector = MVPFraudDetector()
        print("✓ 检测器实例创建成功")
        
        # 尝试加载模型
        try:
            if fraud_api.detector.load_model():
                fraud_api.model_loaded = True
                print("✅ 模型加载成功")
                
                # 验证模型组件
                if hasattr(fraud_api.detector, 'model') and fraud_api.detector.model is not None:
                    print("✓ 主模型加载正常")
                if hasattr(fraud_api.detector, 'feature_columns') and fraud_api.detector.feature_columns is not None:
                    print(f"✓ 特征列加载正常，共 {len(fraud_api.detector.feature_columns)} 个特征")
                if hasattr(fraud_api.detector, 'model_metadata') and fraud_api.detector.model_metadata is not None:
                    print("✓ 模型元数据加载正常")
                
                return True
            else:
                print("⚠️ 未找到保存的模型文件")
                fraud_api.model_loaded = False
                return False
                
        except Exception as load_error:
            print(f"❌ 模型加载过程中出错: {load_error}")
            fraud_api.model_loaded = False
            return False
            
    except ImportError as import_error:
        print(f"❌ 模块导入失败: {import_error}")
        return False
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

# 创建主应用
app = FastAPI(
    title="企业资金流向欺诈检测系统",
    description="包含API服务和Web界面的完整系统",
    version="1.0.0"
)

# 添加CORS中间件 - 更宽松的设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载API路由到/api前缀
app.mount("/api", api_app)

# 确保API应用的startup事件被正确执行
@app.on_event("startup")
async def startup_event():
    """启动事件 - 确保API模型被正确加载"""
    print("🚀 启动Web服务器...")
    # 手动初始化模型
    init_model()

# 静态文件服务
@app.get("/", response_class=HTMLResponse)
async def root():
    """主页 - 重定向到web界面"""
    try:
        html_file = Path(current_dir) / "fraud_detection_web.html"
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        # 修改API URL为相对路径
        content = content.replace('http://localhost:8000', '/api')
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <body>
            <h1>企业资金流向欺诈检测系统</h1>
            <p>❌ 未找到web界面文件 fraud_detection_web.html</p>
            <p>请确保文件存在于当前目录</p>
            <a href="/api/docs">访问API文档</a>
        </body>
        </html>
        """)

@app.get("/demo", response_class=HTMLResponse)
async def demo():
    """演示页面"""
    try:
        html_file = Path(current_dir) / "fraud_detection_demo.html"
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        # 修改API URL为相对路径
        content = content.replace('http://localhost:8000', '/api')
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <body>
            <h1>演示页面</h1>
            <p>❌ 未找到演示文件 fraud_detection_demo.html</p>
            <p><a href="/">返回主页</a></p>
        </body>
        </html>
        """)

@app.get("/favicon.ico")
async def favicon():
    """处理favicon请求，避免404错误"""
    from fastapi.responses import Response
    return Response(status_code=204)  # 返回空响应

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "web_server"}

def print_startup_info():
    """打印启动信息"""
    print("=" * 60)
    print("  🚀 企业资金流向欺诈检测系统")
    print("  Enterprise Fund Flow Fraud Detection System")
    print("=" * 60)
    print()
    print("🌐 服务地址:")
    print("  主页 (Web界面): http://localhost:9000/")
    print("  演示页面:      http://localhost:9000/demo")
    print("  API文档:       http://localhost:9000/api/docs")
    print("  健康检查:      http://localhost:9000/health")
    print()
    print("🔧 API端点:")
    print("  预测接口:      POST http://localhost:9000/api/predict")
    print("  模型信息:      GET  http://localhost:9000/api/model/info")
    print("  API健康:       GET  http://localhost:9000/api/health")
    print()
    print("📝 注意事项:")
    print("  • 系统已自动处理CORS问题")
    print("  • 可以直接在浏览器中使用，无需额外配置")
    print("  • 按 Ctrl+C 停止服务")
    print("=" * 60)
    print()

def main():
    """主函数"""
    print_startup_info()
    
    try:
        # 自动打开浏览器
        print("📂 正在打开浏览器...")
        webbrowser.open('http://localhost:9000/')
        
        # 启动服务器
        print("🚀 启动Web服务器...")
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=9000,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
