# Docker Compose configuration for Enterprise Fund Flow Fraud Detection System
# Provides easy local development and deployment setup

version: '3.8'

services:
  # =============================================================================
  # Main API Service
  # =============================================================================
  fraud-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: fraud-detection:latest
    container_name: fraud-detection-api
    ports:
      - "8000:8000"
    environment:
      # Python configuration
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      
      # LLM Configuration (override in .env file)
      - SILICONFLOW_API_TOKEN=${SILICONFLOW_API_TOKEN:-}
      - SILICONFLOW_API_URL=${SILICONFLOW_API_URL:-https://api.siliconflow.cn/v1/chat/completions}
      - SILICONFLOW_MODEL=${SILICONFLOW_MODEL:-Qwen/QwQ-32B}
      
      # Application configuration
      - DEFAULT_BATCH_SIZE=${DEFAULT_BATCH_SIZE:-20}
      - DEFAULT_TOTAL_SIZE=${DEFAULT_TOTAL_SIZE:-2000}
      - DEFAULT_FRAUD_RATIO=${DEFAULT_FRAUD_RATIO:-0.05}
    volumes:
      # Persist model files
      - ./models:/app/models
      # Persist data files
      - ./data:/app/data
      # Persist logs
      - ./logs:/app/logs
    networks:
      - fraud-detection-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - model-trainer

  # =============================================================================
  # Model Training Service (runs once to ensure models exist)
  # =============================================================================
  model-trainer:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: fraud-detection:latest
    container_name: fraud-detection-trainer
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./models:/app/models
      - ./data:/app/data
    networks:
      - fraud-detection-network
    command: ["python", "whole.py", "1"]
    restart: "no"  # Run once only

  # =============================================================================
  # Web Server Service (alternative to API-only)
  # =============================================================================
  web-server:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: fraud-detection:latest
    container_name: fraud-detection-web
    ports:
      - "9000:9000"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - fraud-detection-network
    command: ["python", "web_server.py"]
    restart: unless-stopped
    profiles:
      - web  # Only start with --profile web
    depends_on:
      - model-trainer

# =============================================================================
# Networks
# =============================================================================
networks:
  fraud-detection-network:
    driver: bridge
    name: fraud-detection-net

# =============================================================================
# Volumes (optional - for named volumes instead of bind mounts)
# =============================================================================
volumes:
  fraud-models:
    driver: local
  fraud-data:
    driver: local
  fraud-logs:
    driver: local
