# 企业资金流向欺诈检测系统

基于机器学习和大模型增强的企业资金流向欺诈检测系统。

## 核心功能

- **智能数据生成**: 使用LLM增强生成更真实的交易数据
- **欺诈检测模型**: 基于XGBoost/RandomForest的机器学习模型
- **API服务**: FastAPI构建的预测服务接口
- **数据管理**: 完整的数据生成、训练、预测流程

## 项目结构

```
├── whole.py              # 主程序入口
├── gen_data.py           # 传统数据生成器
├── llm_enhanced_gen_data.py  # LLM增强数据生成器
├── tra_model.py          # 机器学习模型训练
├── fraud_api.py          # FastAPI服务
├── llm_config.py         # LLM配置文件
├── fields.py             # 字段定义
├── requirements.txt      # 依赖包
├── fraud_detection_web.html    # 完整版Web界面
├── fraud_detection_demo.html   # 离线演示版界面
├── start_system.py      # Python启动脚本（推荐）
├── launcher.bat         # 简化启动器
├── verify_fix.py        # API修复验证脚本
└── models/               # 训练好的模型文件
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行模式选择
```bash
python whole.py 1    # 传统数据生成 + 模型训练
python whole.py 2    # LLM增强数据生成 + 模型训练
python whole.py 3    # 仅生成数据
python whole.py 4    # 仅训练模型
python whole.py 5    # 启动API服务
python whole.py 6    # LLM数据生成 + API服务
```

### 3. Web界面使用

#### 快速启动
```bash
# 推荐: 使用Python启动脚本
python start_system.py

# 或使用简化启动器
launcher.bat

# 或手动启动
python fraud_api.py  # 启动API服务
# 然后打开 fraud_detection_web.html
```

#### 界面说明
- **完整版界面**: `fraud_detection_web.html` - 需要API服务支持
- **演示版界面**: `fraud_detection_demo.html` - 可离线运行，包含典型案例

### 4. API服务使用

启动服务后访问：
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

#### 单笔预测
```bash
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{
    "acntmcode": "COM0001",
    "partycode": "COM0002",
    "amount": 100000.00,
    "or_type_fy": "转账",
    "quarter": "Q1",
    "is_holiday": false,
    "month_day": 15,
    "is_month_end": false
  }'
```

## 配置说明

### LLM配置 (llm_config.py)
```python
SILICONFLOW_API_TOKEN = "your-api-token"
SILICONFLOW_API_URL = "https://api.siliconflow.cn/v1/chat/completions"
SILICONFLOW_MODEL = "Qwen/QwQ-32B"
```

## 特性说明

- **金额精度**: 所有金额自动四舍五入保留两位小数
- **欺诈模式**: 支持循环交易、集中交易、可疑时间等多种欺诈模式检测
- **模型持久化**: 训练完成的模型自动保存，支持增量更新
- **错误处理**: 完善的异常处理和日志记录机制

## 故障排除

### 常见问题

1. **API序列化错误**
   ```
   TypeError: 'numpy.float32' object is not iterable
   ```
   - 解决方案：已修复numpy数据类型序列化问题，重启API服务即可

2. **模型未加载**
   ```
   模型未加载，请先训练模型
   ```
   - 解决方案：运行 `python whole.py 1` 训练模型

3. **Web界面CORS错误**
   ```
   Access-Control-Allow-Origin
   ```
   - 解决方案：已添加CORS支持，确保API服务正常运行

### 验证修复

运行验证脚本检查系统状态：
```bash
python verify_fix.py
```
