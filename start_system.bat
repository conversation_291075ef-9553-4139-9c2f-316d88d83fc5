@echo off
echo ========================================
echo   Enterprise Fund Flow Fraud Detection System
echo   Startup Script
echo ========================================
echo.

echo Please select startup mode:
echo 1. Start API service + Auto open web pages
echo 2. Start API service only
echo 3. Open demo pages only
echo 4. Train model then start service
echo 5. Exit
echo.

set /p choice=Please enter your choice (1-5): 

if "%choice%"=="1" goto start_api_and_web
if "%choice%"=="2" goto start_api_only
if "%choice%"=="3" goto open_web_only
if "%choice%"=="4" goto train_and_start
if "%choice%"=="5" goto exit
goto invalid_choice

:start_api_and_web
echo.
echo Starting API service...
echo Please wait for service startup, web pages will open automatically
echo.
start /b python fraud_api.py
timeout /t 3 /nobreak >nul
echo Opening web interface...
start fraud_detection_web.html
start fraud_detection_demo.html
echo.
echo API service started, access URLs:
echo - API Documentation: http://localhost:8000/docs
echo - Health Check: http://localhost:8000/health
echo - Web interface opened automatically
echo.
echo Press any key to exit...
pause >nul
goto end

:start_api_only
echo.
echo Starting API service...
python fraud_api.py
goto end

:open_web_only
echo.
echo Opening web interface...
start fraud_detection_demo.html
echo Demo page opened!
echo.
echo For full functionality, please start API service first:
echo python fraud_api.py
echo.
pause
goto end

:train_and_start
echo.
echo Training model...
python whole.py 1
echo.
echo Model training completed, starting API service...
start /b python fraud_api.py
timeout /t 3 /nobreak >nul
echo Opening web interface...
start fraud_detection_web.html
echo.
echo System fully started!
echo.
pause
goto end

:invalid_choice
echo.
echo Invalid choice, please run script again
pause
goto end

:exit
echo.
echo Thank you for using!
goto end

:end
echo.
echo Script execution completed
