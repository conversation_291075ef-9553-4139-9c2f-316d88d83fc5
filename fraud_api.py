#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚假关联交易检测API服务
使用FastAPI构建的预测服务
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional
import uvicorn
import pandas as pd
import sys
import os
from datetime import datetime
import logging
import json
import numpy as np

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入自定义模块
try:
    from tra_model import MVPFraudDetector
    from gen_data import MVPDataGenerator
    print("✓ 成功导入模型模块")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 序列化帮助函数
def serialize_for_json(obj):
    """将numpy类型转换为Python原生类型以便JSON序列化"""
    if obj is None:
        return None
    elif isinstance(obj, (np.integer, int)):
        return int(obj)
    elif isinstance(obj, (np.floating, float)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.bool_, bool)):
        return bool(obj)
    elif isinstance(obj, np.str_):
        return str(obj)
    elif isinstance(obj, dict):
        return {str(key): serialize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [serialize_for_json(item) for item in obj]
    elif hasattr(obj, 'item'):  # numpy标量
        return obj.item()
    elif hasattr(obj, '__dict__'):  # 复杂对象
        try:
            return {key: serialize_for_json(value) for key, value in obj.__dict__.items()}
        except:
            return str(obj)
    else:
        try:
            # 尝试转换为Python基本类型
            if isinstance(obj, (str, int, float, bool)):
                return obj
            else:
                return str(obj)
        except:
            return str(obj)

# FastAPI应用
app = FastAPI(
    title="虚假关联交易检测API",
    description="基于机器学习的企业虚假关联交易检测服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
detector = None
model_loaded = False

# 数据模型定义
class CompanyFeatures(BaseModel):
    """企业特征数据模型"""
    company_code: str = Field(..., description="企业代码")
    total_transactions: int = Field(default=0, description="总交易次数")
    total_amount: float = Field(default=0.0, description="总交易金额")
    avg_amount: float = Field(default=0.0, description="平均交易金额")
    max_amount: float = Field(default=0.0, description="最大交易金额")
    min_amount: float = Field(default=0.0, description="最小交易金额")
    amount_std: float = Field(default=0.0, description="金额标准差")
    unique_partners: int = Field(default=0, description="交易对手数量")
    quarter_end_ratio: float = Field(default=0.0, description="季末交易比例")
    weekend_ratio: float = Field(default=0.0, description="周末交易比例")
    circular_partners: int = Field(default=0, description="循环交易对手数")
    high_amount_ratio: float = Field(default=0.0, description="高额交易比例")
    concentration_score: float = Field(default=0.0, description="交易集中度")
    receivable_balance: float = Field(default=0.0, description="应收款余额")
    payable_balance: float = Field(default=0.0, description="应付款余额")
    guarantee_amount: float = Field(default=0.0, description="担保金额")
    internal_ratio: float = Field(default=0.0, description="内部交易比例")

class TransactionData(BaseModel):
    """交易数据模型"""
    from_company: str = Field(..., description="付款企业")
    to_company: str = Field(..., description="收款企业")
    amount: float = Field(..., description="交易金额")
    transaction_type: str = Field(..., description="交易类型")
    date: str = Field(..., description="交易日期 (YYYY-MM-DD)")
    description: Optional[str] = Field(default="", description="交易描述")

class BatchPredictionRequest(BaseModel):
    """批量预测请求模型"""
    companies: List[CompanyFeatures] = Field(..., description="企业特征列表")

class PredictionResponse(BaseModel):
    """预测结果响应模型"""
    company_code: str = Field(..., description="企业代码")
    fraud_probability: float = Field(..., description="欺诈概率")
    predicted_label: int = Field(..., description="预测标签 (0:正常, 1:欺诈)")
    risk_level: str = Field(..., description="风险等级")
    is_suspicious: bool = Field(..., description="是否可疑")
    prediction_time: str = Field(..., description="预测时间")

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时加载模型"""
    global detector, model_loaded
    
    logger.info("正在启动虚假关联交易检测API服务...")
    
    try:
        detector = MVPFraudDetector()
        
        # 尝试加载已保存的模型
        if detector.load_model():
            model_loaded = True
            logger.info("✓ 已加载保存的模型")
        else:
            logger.warning("⚠ 未找到保存的模型，需要先训练模型")
            model_loaded = False
            
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        model_loaded = False

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "虚假关联交易检测API服务",
        "version": "1.0.0",
        "model_loaded": model_loaded,
        "endpoints": {
            "predict": "/predict",
            "predict_batch": "/predict/batch",
            "train": "/train",
            "health": "/health",
            "model_info": "/model/info"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "model_loaded": model_loaded,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/model/info")
async def model_info():
    """获取模型信息"""
    try:
        if not model_loaded or detector is None:
            return {
                "model_loaded": False,
                "error": "模型未加载",
                "feature_count": 0,
                "model_type": "XGBoost",
                "loaded_time": None
            }
        
        # 基础信息
        result = {
            "model_loaded": True,
            "feature_count": 0,
            "model_type": "XGBoost", 
            "loaded_time": datetime.now().isoformat()
        }
        
        # 尝试获取特征数量
        try:
            if hasattr(detector, 'feature_columns') and detector.feature_columns is not None:
                result["feature_count"] = int(len(detector.feature_columns))
                result["feature_columns"] = [str(col) for col in detector.feature_columns[:10]]  # 只显示前10个特征
        except Exception as e:
            logger.warning(f"获取特征数量失败: {e}")
            result["feature_count"] = 0
        
        # 尝试序列化模型元数据
        try:
            if hasattr(detector, 'model_metadata') and detector.model_metadata is not None:
                # 安全地序列化模型元数据
                safe_metadata = {}
                for key, value in detector.model_metadata.items():
                    try:
                        safe_metadata[str(key)] = serialize_for_json(value)
                    except Exception as inner_e:
                        logger.warning(f"序列化元数据键 {key} 失败: {inner_e}")
                        safe_metadata[str(key)] = str(value)
                
                result["model_info"] = safe_metadata
            else:
                result["model_info"] = {"status": "no_metadata"}
        except Exception as e:
            logger.warning(f"序列化模型元数据失败: {e}")
            result["model_info"] = {"error": f"序列化失败: {str(e)}"}
        
        # 确保所有值都是JSON可序列化的
        final_result = serialize_for_json(result)
        return final_result
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        import traceback
        traceback.print_exc()
        # 返回安全的错误信息
        return {
            "model_loaded": False,
            "error": f"内部错误: {str(e)}",
            "feature_count": 0,
            "model_type": "XGBoost",
            "loaded_time": None
        }

@app.post("/predict", response_model=PredictionResponse)
async def predict_fraud(features: CompanyFeatures):
    """预测单个企业的欺诈概率"""
    if not model_loaded or detector is None:
        raise HTTPException(status_code=400, detail="模型未加载，请先训练模型")
    
    try:
        # 转换为字典
        features_dict = features.dict()
        
        # 进行预测
        result = detector.predict_fraud_probability(features_dict)
        
        # 构建响应，确保数据类型转换
        response = PredictionResponse(
            company_code=features.company_code,
            fraud_probability=float(result['fraud_probability']),
            predicted_label=int(result['predicted_label']),
            risk_level=str(result['risk_level']),
            is_suspicious=bool(result['is_suspicious']),
            prediction_time=datetime.now().isoformat()
        )
        
        logger.info(f"预测完成: {features.company_code} -> {result['fraud_probability']:.4f}")
        
        return response
        
    except Exception as e:
        logger.error(f"预测失败: {e}")
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")

@app.post("/predict/batch")
async def predict_fraud_batch(request: BatchPredictionRequest):
    """批量预测企业欺诈概率"""
    if not model_loaded or detector is None:
        raise HTTPException(status_code=400, detail="模型未加载，请先训练模型")
    
    try:
        # 转换为DataFrame
        companies_data = [company.dict() for company in request.companies]
        df = pd.DataFrame(companies_data)
        
        # 批量预测
        results_df = detector.predict_companies_batch(df)
        
        # 构建响应
        responses = []
        for _, row in results_df.iterrows():
            response = PredictionResponse(
                company_code=str(row['company_code']),
                fraud_probability=float(round(row['fraud_probability'], 4)),
                predicted_label=int(row['predicted_label']),
                risk_level=str(row['risk_level']),
                is_suspicious=bool(row['is_suspicious']),
                prediction_time=datetime.now().isoformat()
            )
            responses.append(response)
        
        logger.info(f"批量预测完成: {len(responses)} 家企业")
        
        return {"predictions": responses, "total_count": len(responses)}
        
    except Exception as e:
        logger.error(f"批量预测失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量预测失败: {str(e)}")

@app.post("/train")
async def train_model(n_samples: int = 1000, fraud_ratio: float = 0.05):
    """训练新模型"""
    global detector, model_loaded
    
    try:
        logger.info(f"开始训练模型: {n_samples} 样本, {fraud_ratio:.1%} 欺诈比例")
        
        # 生成训练数据
        generator = MVPDataGenerator()
        settlement_df = generator.generate_settlement_data(n_total=n_samples, fraud_ratio=fraud_ratio)
        receivable_df = generator.generate_receivable_data(n_samples//5)
        payable_df = generator.generate_payable_data(n_samples//5)
        guarantee_df = generator.generate_guarantee_data(n_samples//10)
        truth_labels = generator.generate_truth_labels(settlement_df)
        
        # 创建特征
        detector = MVPFraudDetector()
        features_df = detector.create_mvp_features(settlement_df, receivable_df, payable_df, guarantee_df)
        
        # 训练模型
        results = detector.train_mvp_model(features_df, truth_labels, settlement_df)
        
        model_loaded = True
        
        logger.info(f"模型训练完成: AUC = {results['auc']:.4f}")
        
        return {
            "message": "模型训练完成",
            "auc": results['auc'],
            "train_samples": len(features_df),
            "fraud_ratio": fraud_ratio,
            "model_saved": True
        }
        
    except Exception as e:
        logger.error(f"模型训练失败: {e}")
        raise HTTPException(status_code=500, detail=f"模型训练失败: {str(e)}")

@app.post("/analyze/transaction")
async def analyze_transaction(transaction: TransactionData):
    """分析单笔交易的风险"""
    try:
        # 这里可以基于交易数据构建特征
        # 简化版本：基于金额和交易类型进行基础风险评估
        
        risk_score = 0.0
        risk_factors = []
        
        # 金额风险分析
        if transaction.amount >= 1000000:
            risk_score += 0.3
            risk_factors.append("大额交易")
        elif transaction.amount <= 1000:
            risk_score += 0.1
            risk_factors.append("小额交易")
        
        # 整数金额风险
        if transaction.amount == int(transaction.amount):
            risk_score += 0.2
            risk_factors.append("整数金额")
        
        # 交易类型风险
        if transaction.transaction_type in ["往来款", "代付款"]:
            risk_score += 0.3
            risk_factors.append("高风险交易类型")
        
        # 日期风险分析
        try:
            trans_date = datetime.strptime(transaction.date, "%Y-%m-%d")
            # 月末/季末交易
            if trans_date.day >= 28 or (trans_date.month in [3,6,9,12] and trans_date.day >= 25):
                risk_score += 0.2
                risk_factors.append("季末/月末交易")
        except:
            pass
        
        # 风险等级
        if risk_score >= 0.8:
            risk_level = "极高"
        elif risk_score >= 0.6:
            risk_level = "高"
        elif risk_score >= 0.4:
            risk_level = "中"
        elif risk_score >= 0.2:
            risk_level = "低"
        else:
            risk_level = "极低"
        
        return {
            "transaction_id": f"{transaction.from_company}_{transaction.to_company}_{transaction.date}",
            "risk_score": round(risk_score, 4),
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "is_suspicious": risk_score >= 0.5,
            "analysis_time": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"交易分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"交易分析失败: {str(e)}")

# 运行服务
if __name__ == "__main__":
    print("启动虚假关联交易检测API服务...")
    print("访问 http://localhost:8000/docs 查看API文档")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        reload=False  # 生产环境建议设为False
    )
