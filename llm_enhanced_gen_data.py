import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import string
import json
import requests
import time
import os
from typing import List, Dict, Any

# 尝试导入配置文件
try:
    import importlib.util
    current_dir = os.path.dirname(os.path.abspath(__file__))
    spec = importlib.util.spec_from_file_location("llm_config", os.path.join(current_dir, "llm_config.py"))
    if spec and spec.loader:
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # 从配置文件读取参数
        DEFAULT_API_TOKEN = config_module.SILICONFLOW_API_TOKEN
        DEFAULT_API_URL = config_module.SILICONFLOW_API_URL
        DEFAULT_MODEL = config_module.SILICONFLOW_MODEL
        DEFAULT_BATCH_SIZE = config_module.DEFAULT_BATCH_SIZE
        API_RETRY_COUNT = config_module.API_RETRY_COUNT
        API_TIMEOUT = config_module.API_TIMEOUT
        TEMPERATURE = config_module.TEMPERATURE
        MAX_TOKENS = config_module.MAX_TOKENS
        print("✓ 成功加载LLM配置文件")
    else:
        raise ImportError("配置文件加载失败")
except ImportError:
    # 如果没有配置文件，使用默认值
    DEFAULT_API_TOKEN = "sk-dceyienvrazgavqubkwwxtlmsbseqlgmswqlowjfbbiyeenl"
    DEFAULT_API_URL = "https://api.siliconflow.cn/v1/chat/completions"
    DEFAULT_MODEL = "Qwen/QwQ-32B"
    DEFAULT_BATCH_SIZE = 20
    API_RETRY_COUNT = 3
    API_TIMEOUT = 30
    TEMPERATURE = 0.8
    MAX_TOKENS = 1000
    print("⚠ 未找到配置文件，使用默认配置")

class LLMEnhancedDataGenerator:
    """使用大模型增强的数据生成器"""
    
    def __init__(self, seed=42, api_token=None):
        np.random.seed(seed)
        random.seed(seed)
        
        # API配置
        self.api_url = DEFAULT_API_URL
        self.api_token = api_token or DEFAULT_API_TOKEN
        self.model = DEFAULT_MODEL
        
        # 检查API Token
        if self.api_token == "sk-your-token":
            print("⚠ 检测到默认API Token，请在llm_config.py中配置实际Token")
        
        # 基础企业池
        self.companies = [f'COM{str(i).zfill(4)}' for i in range(1, 101)]  # 100家企业
        self.company_names = [f'企业{chr(65+i%26)}{str(i//26+1)}' for i in range(100)]
        
        # 欺诈组合
        self.fraud_groups = [
            ['COM0001', 'COM0002', 'COM0003'],
            ['COM0004', 'COM0005'],
            ['COM0006', 'COM0007', 'COM0008'],
            ['COM0009', 'COM0010'],
            ['COM0011', 'COM0012', 'COM0013'],
        ]
        
        # 时间范围
        self.quarters = [
            (datetime(2024, 1, 1), datetime(2024, 3, 31)),
            (datetime(2024, 4, 1), datetime(2024, 6, 30)),
            (datetime(2024, 7, 1), datetime(2024, 9, 30)),
            (datetime(2024, 10, 1), datetime(2024, 12, 31)),
        ]
        
        # 缓存生成的数据样本
        self.sample_cache = []
        
    def call_llm(self, prompt: str, max_retries: int = API_RETRY_COUNT) -> str:
        """调用大模型API"""
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": TEMPERATURE,  # 使用配置文件中的随机性
            "max_tokens": MAX_TOKENS
        }
        
        for attempt in range(max_retries):
            try:
                response = requests.post(self.api_url, headers=headers, json=data, timeout=API_TIMEOUT)
                response.raise_for_status()
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    print(f"API响应格式异常: {result}")
                    
            except requests.exceptions.RequestException as e:
                print(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    
        # 如果API调用失败，返回默认值
        return self._generate_fallback_response()
    
    def _generate_fallback_response(self) -> str:
        """API失败时的后备方案"""
        return json.dumps({
            "transactions": [
                {
                    "amount": round(random.uniform(10000, 500000), 2),  # 四舍五入保留两位小数
                    "type": random.choice(["收入", "支出", "转账"]),
                    "description": "正常业务往来",
                    "risk_level": "low"
                }
            ]
        })
    
    def generate_llm_enhanced_settlement_data(self, n_total=1000, fraud_ratio=0.05, batch_size=50):
        """使用大模型增强的资金结算数据生成"""
        print(f"开始生成LLM增强的数据，总量: {n_total}, 欺诈比例: {fraud_ratio:.1%}")
        
        all_data = []
        n_fraud = int(n_total * fraud_ratio)
        n_normal = n_total - n_fraud
        
        # 1. 生成初始种子数据 (10%)
        seed_data = self._generate_seed_data(min(100, n_total // 10))
        all_data.extend(seed_data)
        print(f"✓ 生成种子数据: {len(seed_data)} 条")
        
        # 2. 使用LLM生成正常交易数据
        remaining_normal = n_normal - len([d for d in seed_data if d['transaction_type'] == 'normal'])
        if remaining_normal > 0:
            normal_data = self._generate_llm_normal_transactions(remaining_normal, batch_size)
            all_data.extend(normal_data)
            print(f"✓ 生成LLM增强正常交易: {len(normal_data)} 条")
        
        # 3. 使用LLM生成欺诈交易数据
        remaining_fraud = n_fraud - len([d for d in seed_data if d['transaction_type'].startswith('fraud')])
        if remaining_fraud > 0:
            fraud_data = self._generate_llm_fraud_transactions(remaining_fraud, batch_size)
            all_data.extend(fraud_data)
            print(f"✓ 生成LLM增强欺诈交易: {len(fraud_data)} 条")
        
        # 4. 打乱数据
        random.shuffle(all_data)
        
        print(f"✓ 数据生成完成，总计: {len(all_data)} 条")
        return pd.DataFrame(all_data)
    
    def _generate_seed_data(self, n_seed: int) -> List[Dict]:
        """生成种子数据"""
        seed_data = []
        
        for _ in range(n_seed):
            from_company = random.choice(self.companies)
            to_company = random.choice([c for c in self.companies if c != from_company])
            quarter_start, quarter_end = random.choice(self.quarters)
            trade_date = self._random_date_in_range(quarter_start, quarter_end)
            
            is_fraud = random.random() < 0.05  # 5%种子欺诈
            
            seed_data.append({
                'acntmcode': from_company,
                'acntmname': self.company_names[int(from_company[3:]) - 1],
                'partycode': to_company,
                'receiveunitname': self.company_names[int(to_company[3:]) - 1],
                'amount': round(random.uniform(10000, 1000000), 2),  # 四舍五入保留两位小数
                'or_type_fy': random.choice(['收入', '支出', '转账', '借款', '还款']),
                'banktradedare': trade_date,
                'dayidx': trade_date.strftime('%Y-%m-%d'),
                'transaction_type': 'fraud_seed' if is_fraud else 'normal',
                'quarter': f"Q{((trade_date.month-1)//3)+1}",
                'is_holiday': trade_date.weekday() >= 5,
                'month_day': trade_date.day,
                'is_month_end': trade_date.day >= 25
            })
        
        return seed_data
    
    def _generate_llm_normal_transactions(self, n_normal: int, batch_size: int) -> List[Dict]:
        """使用LLM生成正常交易"""
        normal_data = []
        
        for batch_start in range(0, n_normal, batch_size):
            batch_end = min(batch_start + batch_size, n_normal)
            batch_size_actual = batch_end - batch_start
            
            # 随机选择一些种子数据作为参考
            sample_data = random.sample(self.sample_cache, min(3, len(self.sample_cache))) if self.sample_cache else []
            
            prompt = self._create_normal_transaction_prompt(batch_size_actual, sample_data)
            
            try:
                llm_response = self.call_llm(prompt)
                batch_data = self._parse_llm_response_to_transactions(llm_response, 'normal')
                normal_data.extend(batch_data)
                
                # 更新样本缓存
                self.sample_cache.extend(batch_data[:2])  # 保留最近的样本
                if len(self.sample_cache) > 20:
                    self.sample_cache = self.sample_cache[-20:]  # 保持缓存大小
                    
                print(f"  批次 {batch_start//batch_size + 1}: 生成 {len(batch_data)} 条正常交易")
                time.sleep(1)  # 避免API限流
                
            except Exception as e:
                print(f"  批次生成失败，使用后备方案: {e}")
                fallback_data = self._generate_fallback_normal_transactions(batch_size_actual)
                normal_data.extend(fallback_data)
        
        return normal_data
    
    def _generate_llm_fraud_transactions(self, n_fraud: int, batch_size: int) -> List[Dict]:
        """使用LLM生成欺诈交易"""
        fraud_data = []
        
        for batch_start in range(0, n_fraud, batch_size):
            batch_end = min(batch_start + batch_size, n_fraud)
            batch_size_actual = batch_end - batch_start
            
            # 选择欺诈模式
            fraud_pattern = random.choice(['circular', 'concentrated', 'suspicious_timing', 'amount_anomaly'])
            
            prompt = self._create_fraud_transaction_prompt(batch_size_actual, fraud_pattern)
            
            try:
                llm_response = self.call_llm(prompt)
                batch_data = self._parse_llm_response_to_transactions(llm_response, f'fraud_{fraud_pattern}')
                fraud_data.extend(batch_data)
                
                print(f"  欺诈批次 {batch_start//batch_size + 1} ({fraud_pattern}): 生成 {len(batch_data)} 条")
                time.sleep(1)
                
            except Exception as e:
                print(f"  欺诈批次生成失败，使用后备方案: {e}")
                fallback_data = self._generate_fallback_fraud_transactions(batch_size_actual, fraud_pattern)
                fraud_data.extend(fallback_data)
        
        return fraud_data
    
    def _create_normal_transaction_prompt(self, batch_size: int, sample_data: List[Dict]) -> str:
        """创建正常交易生成提示"""
        sample_text = ""
        if sample_data:
            sample_text = f"参考以下样本数据风格：\n{json.dumps(sample_data[:2], ensure_ascii=False, indent=2)}\n\n"
        
        return f"""
{sample_text}请生成 {batch_size} 条真实的企业间资金交易记录。要求：

1. 交易模式要多样化：正常的商业往来、供应商付款、客户收款、设备采购等
2. 金额要符合企业实际业务规模，有小额(1-10万)、中额(10-100万)、大额(100万+)的合理分布
3. 交易时间要分散，避免过于集中
4. 企业代码格式：COM0001-COM0100
5. 交易类型包括：收入、支出、转账、借款、还款、设备款、货款、服务费等

请以JSON格式返回，结构如下：
{{
  "transactions": [
    {{
      "from_company": "COM0001",
      "to_company": "COM0002", 
      "amount": 285000,
      "transaction_type": "货款",
      "date": "2024-03-15",
      "description": "采购原材料款项",
      "business_nature": "正常商业往来"
    }}
  ]
}}
"""

    def _create_fraud_transaction_prompt(self, batch_size: int, fraud_pattern: str) -> str:
        """创建欺诈交易生成提示"""
        pattern_descriptions = {
            'circular': '循环资金流：资金在几个关联企业间循环流转，最终回到起点',
            'concentrated': '集中交易：在特定时间点(如季末、月末)集中发生大额交易',
            'suspicious_timing': '可疑时间：在非工作时间、节假日进行异常交易',
            'amount_anomaly': '金额异常：交易金额存在明显的人为痕迹，如整数金额、异常大额等'
        }
        
        return f"""
请生成 {batch_size} 条涉嫌虚假关联交易的数据，模式：{pattern_descriptions[fraud_pattern]}

要求：
1. 模拟真实的欺诈行为，但要有一定隐蔽性
2. 金额要看起来"不太正常"但又不过于明显
3. 时间选择要体现欺诈特征
4. 企业代码范围：COM0001-COM0100
5. 要包含一些掩饰性的描述

请以JSON格式返回：
{{
  "transactions": [
    {{
      "from_company": "COM0001",
      "to_company": "COM0002",
      "amount": 999999,
      "transaction_type": "往来款",
      "date": "2024-03-31",
      "description": "业务合作款",
      "fraud_indicators": ["金额接近整数", "季末交易"]
    }}
  ]
}}
"""

    def _parse_llm_response_to_transactions(self, response: str, transaction_type: str) -> List[Dict]:
        """解析LLM响应为交易数据"""
        try:
            # 尝试提取JSON
            response_clean = response.strip()
            if '```json' in response_clean:
                start = response_clean.find('```json') + 7
                end = response_clean.find('```', start)
                response_clean = response_clean[start:end]
            elif '```' in response_clean:
                start = response_clean.find('```') + 3
                end = response_clean.find('```', start)
                response_clean = response_clean[start:end]
            
            parsed_data = json.loads(response_clean)
            transactions = parsed_data.get('transactions', [])
            
            result = []
            for trans in transactions:
                # 转换为标准格式
                trade_date = datetime.strptime(trans.get('date', '2024-06-15'), '%Y-%m-%d')
                
                result.append({
                    'acntmcode': trans.get('from_company', f'COM{random.randint(1,100):04d}'),
                    'acntmname': f"企业{trans.get('from_company', 'COM0001')[3:]}",
                    'partycode': trans.get('to_company', f'COM{random.randint(1,100):04d}'),
                    'receiveunitname': f"企业{trans.get('to_company', 'COM0002')[3:]}",
                    'amount': round(float(trans.get('amount', random.uniform(10000, 500000))), 2),  # 四舍五入保留两位小数
                    'or_type_fy': trans.get('transaction_type', '转账'),
                    'banktradedare': trade_date,
                    'dayidx': trade_date.strftime('%Y-%m-%d'),
                    'transaction_type': transaction_type,
                    'quarter': f"Q{((trade_date.month-1)//3)+1}",
                    'is_holiday': trade_date.weekday() >= 5,
                    'month_day': trade_date.day,
                    'is_month_end': trade_date.day >= 25,
                    'llm_generated': True,
                    'description': trans.get('description', ''),
                    'fraud_indicators': trans.get('fraud_indicators', []) if transaction_type.startswith('fraud') else []
                })
            
            return result
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            print(f"解析LLM响应失败: {e}")
            # 返回后备数据
            return self._generate_fallback_normal_transactions(1) if 'normal' in transaction_type else self._generate_fallback_fraud_transactions(1, 'circular')
    
    def _generate_fallback_normal_transactions(self, n: int) -> List[Dict]:
        """生成后备正常交易数据"""
        data = []
        for _ in range(n):
            from_company = random.choice(self.companies)
            to_company = random.choice([c for c in self.companies if c != from_company])
            quarter_start, quarter_end = random.choice(self.quarters)
            trade_date = self._random_date_in_range(quarter_start, quarter_end)
            
            data.append({
                'acntmcode': from_company,
                'acntmname': self.company_names[int(from_company[3:]) - 1],
                'partycode': to_company,
                'receiveunitname': self.company_names[int(to_company[3:]) - 1],
                'amount': round(random.uniform(10000, 500000), 2),  # 四舍五入保留两位小数
                'or_type_fy': random.choice(['收入', '支出', '转账']),
                'banktradedare': trade_date,
                'dayidx': trade_date.strftime('%Y-%m-%d'),
                'transaction_type': 'normal',
                'quarter': f"Q{((trade_date.month-1)//3)+1}",
                'is_holiday': trade_date.weekday() >= 5,
                'month_day': trade_date.day,
                'is_month_end': trade_date.day >= 25,
                'llm_generated': False
            })
        return data
    
    def _generate_fallback_fraud_transactions(self, n: int, pattern: str) -> List[Dict]:
        """生成后备欺诈交易数据"""
        data = []
        for _ in range(n):
            group = random.choice(self.fraud_groups)
            from_company = random.choice(group)
            to_company = random.choice([c for c in group if c != from_company])
            
            # 季末集中交易
            quarter_end = random.choice([
                datetime(2024, 3, 31), datetime(2024, 6, 30),
                datetime(2024, 9, 30), datetime(2024, 12, 31)
            ])
            trade_date = quarter_end - timedelta(days=random.randint(0, 3))
            
            data.append({
                'acntmcode': from_company,
                'acntmname': self.company_names[int(from_company[3:]) - 1],
                'partycode': to_company,
                'receiveunitname': self.company_names[int(to_company[3:]) - 1],
                'amount': round(random.choice([999999.00, 888888.00, 1000000.00, 500000.00]), 2),  # 可疑金额，保留两位小数
                'or_type_fy': '往来款',
                'banktradedare': trade_date,
                'dayidx': trade_date.strftime('%Y-%m-%d'),
                'transaction_type': f'fraud_{pattern}',
                'quarter': f"Q{((trade_date.month-1)//3)+1}",
                'is_holiday': trade_date.weekday() >= 5,
                'month_day': trade_date.day,
                'is_month_end': trade_date.day >= 25,
                'llm_generated': False
            })
        return data
    
    def _random_date_in_range(self, start_date: datetime, end_date: datetime) -> datetime:
        """在指定范围内生成随机日期"""
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        return start_date + timedelta(days=random_days)
    
    def generate_receivable_data(self, n=100):
        """生成应收款项数据 - 保持与原版兼容"""
        data = []
        for _ in range(n):
            company = random.choice(self.companies)
            data.append({
                'acntmcode': company,
                'acntmname': self.company_names[int(company[3:]) - 1],
                'amount': round(random.uniform(50000, 2000000), 2),  # 四舍五入保留两位小数
                'aging_days': random.randint(1, 365),
                'customer_type': random.choice(['关联方', '第三方']),
                'collection_difficulty': random.choice(['容易', '一般', '困难'])
            })
        return pd.DataFrame(data)
    
    def generate_payable_data(self, n=100):
        """生成应付款项数据 - 保持与原版兼容"""
        data = []
        for _ in range(n):
            company = random.choice(self.companies)
            data.append({
                'acntmcode': company,
                'acntmname': self.company_names[int(company[3:]) - 1],
                'amount': round(random.uniform(30000, 1500000), 2),  # 四舍五入保留两位小数
                'aging_days': random.randint(1, 180),
                'supplier_type': random.choice(['关联方', '第三方']),
                'payment_priority': random.choice(['高', '中', '低'])
            })
        return pd.DataFrame(data)
    
    def generate_guarantee_data(self, n=50):
        """生成担保数据 - 保持与原版兼容"""
        data = []
        for _ in range(n):
            guarantor = random.choice(self.companies)
            guaranteed = random.choice([c for c in self.companies if c != guarantor])
            data.append({
                'guarantor_code': guarantor,
                'guarantor_name': self.company_names[int(guarantor[3:]) - 1],
                'guaranteed_code': guaranteed,
                'guaranteed_name': self.company_names[int(guaranteed[3:]) - 1],
                'guarantee_amount': round(random.uniform(100000, 5000000), 2),  # 四舍五入保留两位小数
                'guarantee_type': random.choice(['连带责任', '一般保证']),
                'risk_level': random.choice(['低', '中', '高'])
            })
        return pd.DataFrame(data)
    
    def generate_truth_labels(self, settlement_df):
        """生成真实标签 - 保持与原版兼容"""
        labels = []
        for _, row in settlement_df.iterrows():
            is_fraud = row['transaction_type'].startswith('fraud')
            labels.append({
                'acntmcode': row['acntmcode'],
                'partycode': row['partycode'],
                'dayidx': row['dayidx'],
                'is_fraud': is_fraud,
                'fraud_type': row['transaction_type'] if is_fraud else 'normal',
                'confidence': random.uniform(0.7, 1.0) if is_fraud else random.uniform(0.0, 0.3)
            })
        return pd.DataFrame(labels)
