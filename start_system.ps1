# 企业资金流向欺诈检测系统启动脚本
# PowerShell版本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  企业资金流向欺诈检测系统" -ForegroundColor Yellow
Write-Host "  Enterprise Fund Flow Fraud Detection" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "请选择启动模式 / Please select startup mode:" -ForegroundColor Green
Write-Host "1. 启动API + 打开网页 (Start API + Open Web)" -ForegroundColor White
Write-Host "2. 仅启动API服务 (API Service Only)" -ForegroundColor White
Write-Host "3. 仅打开演示页面 (Demo Pages Only)" -ForegroundColor White
Write-Host "4. 训练模型 + 启动服务 (Train Model + Start Service)" -ForegroundColor White
Write-Host "5. 退出 (Exit)" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请输入选择 (1-5) / Enter choice (1-5)"

switch ($choice) {
    "1" {
        Write-Host "正在启动API服务... / Starting API service..." -ForegroundColor Yellow
        Write-Host "等待服务启动后将自动打开网页 / Web pages will open after service starts" -ForegroundColor Yellow
        
        # 启动API服务（后台）
        Start-Process python -ArgumentList "fraud_api.py" -WindowStyle Minimized
        
        # 等待3秒
        Start-Sleep -Seconds 3
        
        Write-Host "正在打开网页界面... / Opening web interface..." -ForegroundColor Yellow
        
        # 打开网页
        Start-Process "fraud_detection_web.html"
        Start-Process "fraud_detection_demo.html"
        
        Write-Host ""
        Write-Host "系统已启动！/ System started!" -ForegroundColor Green
        Write-Host "API文档 / API Docs: http://localhost:8000/docs" -ForegroundColor Cyan
        Write-Host "健康检查 / Health Check: http://localhost:8000/health" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "按任意键退出 / Press any key to exit..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    
    "2" {
        Write-Host "正在启动API服务... / Starting API service..." -ForegroundColor Yellow
        python fraud_api.py
    }
    
    "3" {
        Write-Host "正在打开演示页面... / Opening demo pages..." -ForegroundColor Yellow
        Start-Process "fraud_detection_demo.html"
        Write-Host ""
        Write-Host "演示页面已打开！/ Demo pages opened!" -ForegroundColor Green
        Write-Host "如需完整功能，请选择选项1启动API服务" -ForegroundColor Yellow
        Write-Host "For full functionality, choose option 1 to start API service" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "按任意键退出 / Press any key to exit..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    
    "4" {
        Write-Host "正在训练模型... / Training model..." -ForegroundColor Yellow
        python whole.py 1
        
        Write-Host "模型训练完成，启动API服务... / Model training completed, starting API..." -ForegroundColor Yellow
        Start-Process python -ArgumentList "fraud_api.py" -WindowStyle Minimized
        
        Start-Sleep -Seconds 3
        
        Write-Host "正在打开网页... / Opening web pages..." -ForegroundColor Yellow
        Start-Process "fraud_detection_web.html"
        
        Write-Host ""
        Write-Host "系统完全启动！/ System fully started!" -ForegroundColor Green
        Write-Host ""
        Write-Host "按任意键退出 / Press any key to exit..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    
    "5" {
        Write-Host "感谢使用！/ Thank you!" -ForegroundColor Green
        exit
    }
    
    default {
        Write-Host "无效选择，请重新运行脚本 / Invalid choice, please run script again" -ForegroundColor Red
        Write-Host ""
        Write-Host "按任意键退出 / Press any key to exit..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
}

Write-Host ""
Write-Host "脚本执行完成 / Script execution completed" -ForegroundColor Gray
