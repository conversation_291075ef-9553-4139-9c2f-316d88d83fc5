@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo   企业资金流向欺诈检测系统
echo   启动脚本 (解决CORS问题版本)
echo ==========================================
echo.

echo 🔧 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 📦 检查依赖包...
python -c "import fastapi, uvicorn" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要依赖，正在安装...
    pip install fastapi uvicorn
)

echo.
echo 选择启动方式:
echo 1. 启动完整Web服务 (推荐 - 无CORS问题)
echo 2. 启动原版API服务 (可能有CORS问题)
echo 3. 仅训练模型
echo 4. 退出
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动完整Web服务...
    echo 📱 浏览器将自动打开 http://localhost:9000/
    echo ⏸️  按 Ctrl+C 停止服务
    echo.
    python web_server.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动原版API服务...
    echo 📱 请手动打开: fraud_detection_web.html
    echo ⚠️  注意: 可能遇到CORS问题
    echo.
    python fraud_api.py
) else if "%choice%"=="3" (
    echo.
    echo 🤖 开始训练模型...
    python tra_model.py
    echo ✅ 模型训练完成
    pause
) else if "%choice%"=="4" (
    echo 👋 退出
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

pause
