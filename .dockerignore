# Docker ignore file for Enterprise Fund Flow Fraud Detection System
# Excludes unnecessary files from Docker build context to reduce build time and image size

# =============================================================================
# Version Control
# =============================================================================
.git
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# Python
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# =============================================================================
# IDE and Editor files
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# =============================================================================
# Documentation and README files (keep only essential ones)
# =============================================================================
*.md
!README.md
docs/
*.txt
!requirements.txt

# =============================================================================
# Logs and temporary files
# =============================================================================
*.log
logs/
tmp/
temp/

# =============================================================================
# Windows specific
# =============================================================================
*.bat
*.ps1

# =============================================================================
# Development and testing files
# =============================================================================
test_*.py
*_test.py
tests/
.pytest_cache/
debug_*.py
verify_*.py

# =============================================================================
# Data files (exclude large datasets, keep model files if small)
# =============================================================================
*.csv
*.json
*.xlsx
*.xls
data/
datasets/

# Keep essential model files but exclude if too large
# Uncomment the following lines if model files are too large for container
# models/*.pkl
# *.pkl

# =============================================================================
# Docker related files
# =============================================================================
Dockerfile*
.dockerignore
docker-compose*.yml
.docker/

# =============================================================================
# CI/CD and deployment
# =============================================================================
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile
deploy/
k8s/
kubernetes/

# =============================================================================
# Other
# =============================================================================
.env.*
.local
*.bak
*.backup
*.orig
