# Docker Compose override for development
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  # =============================================================================
  # Development overrides for integrated service
  # =============================================================================
  fraud-detection:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    volumes:
      # Mount source code for live development
      - .:/app
      # But exclude these directories to avoid conflicts
      - /app/__pycache__
      - /app/.git
    environment:
      # Development-specific settings
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - PYTHONDONTWRITEBYTECODE=1
    ports:
      - "8000:8000"
    command: ["python", "-m", "uvicorn", "fraud_api:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    restart: "no"  # Don't auto-restart in dev mode

  # =============================================================================
  # Development database (optional - for future use)
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: fraud-detection-db
    environment:
      - POSTGRES_DB=fraud_detection
      - POSTGRES_USER=fraud_user
      - POSTGRES_PASSWORD=fraud_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - fraud-detection-network
    profiles:
      - database  # Only start with --profile database

  # =============================================================================
  # Redis for caching (optional - for future use)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: fraud-detection-redis
    ports:
      - "6379:6379"
    networks:
      - fraud-detection-network
    profiles:
      - cache  # Only start with --profile cache

  # =============================================================================
  # Development tools container
  # =============================================================================
  dev-tools:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder  # Use builder stage with all tools
    container_name: fraud-detection-dev-tools
    volumes:
      - .:/app
    working_dir: /app
    networks:
      - fraud-detection-network
    command: ["sleep", "infinity"]  # Keep container running
    profiles:
      - tools  # Only start with --profile tools

volumes:
  postgres_data:
    driver: local
