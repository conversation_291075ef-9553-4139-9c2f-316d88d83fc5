# Multi-stage Dockerfile for Enterprise Fund Flow Fraud Detection System
# Supports both linux/amd64 and linux/arm64 architectures

# =============================================================================
# Build Stage - Install dependencies and build wheels
# =============================================================================
FROM python:3.11 as builder

# Set build arguments for multi-platform support
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# Install system dependencies required for building Python packages
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better layer caching
COPY requirements.txt .

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install wheel for better package building
RUN pip install --no-cache-dir --upgrade pip wheel

# Install Python dependencies
# Use --no-deps for some packages to avoid conflicts and reduce build time
RUN pip install --no-cache-dir -r requirements.txt

# =============================================================================
# Runtime Stage - Minimal production image
# =============================================================================
FROM python:3.11-slim as runtime

# Set build arguments for multi-platform support
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# Add metadata labels
LABEL maintainer="Enterprise Fund Flow Fraud Detection Team"
LABEL description="Multi-platform fraud detection system with FastAPI"
LABEL version="1.0.0"
LABEL architecture="${TARGETPLATFORM}"

# Install only runtime system dependencies
RUN apt-get update && apt-get install -y \
    # Required for some Python packages at runtime
    libgomp1 \
    # Useful for debugging (optional, can be removed for smaller image)
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY --chown=appuser:appuser . .

# Create necessary directories with proper permissions
RUN mkdir -p /app/models /app/data /app/logs \
    && chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Set Python environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Expose port for FastAPI
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command - can be overridden
CMD ["python", "fraud_api.py"]

# =============================================================================
# Alternative entry points (can be used with docker run --entrypoint)
# =============================================================================
# For training model: docker run --entrypoint python image:tag whole.py 1
# For web server: docker run --entrypoint python image:tag web_server.py
# For interactive mode: docker run -it --entrypoint python image:tag start_system.py
