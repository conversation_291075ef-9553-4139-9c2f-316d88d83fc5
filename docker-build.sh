#!/bin/bash
# Multi-platform Docker build script for Enterprise Fund Flow Fraud Detection System
# Supports building for both linux/amd64 and linux/arm64 architectures

set -e  # Exit on any error

# =============================================================================
# Configuration
# =============================================================================
IMAGE_NAME="fraud-detection"
IMAGE_TAG="latest"
REGISTRY=""  # Set this to your registry (e.g., "your-registry.com/")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# Helper Functions
# =============================================================================
print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}  Multi-Platform Docker Build Script${NC}"
    echo -e "${BLUE}  Enterprise Fund Flow Fraud Detection${NC}"
    echo -e "${BLUE}============================================${NC}"
    echo
}

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_docker_buildx() {
    if ! docker buildx version >/dev/null 2>&1; then
        print_error "Docker buildx is not available. Please install Docker Desktop or enable buildx."
        exit 1
    fi
    print_step "Docker buildx is available"
}

setup_buildx() {
    print_step "Setting up Docker buildx builder..."
    
    # Create a new builder instance if it doesn't exist
    if ! docker buildx inspect multiplatform-builder >/dev/null 2>&1; then
        docker buildx create --name multiplatform-builder --driver docker-container --bootstrap
        print_step "Created new buildx builder: multiplatform-builder"
    else
        print_step "Using existing buildx builder: multiplatform-builder"
    fi
    
    # Use the builder
    docker buildx use multiplatform-builder
}

# =============================================================================
# Build Functions
# =============================================================================
build_local() {
    local platform=$1
    local tag_suffix=$2
    
    print_step "Building for platform: $platform"
    
    docker buildx build \
        --platform "$platform" \
        --tag "${REGISTRY}${IMAGE_NAME}:${IMAGE_TAG}${tag_suffix}" \
        --load \
        .
    
    print_step "Build completed for $platform"
}

build_multiplatform() {
    local push_flag=$1
    
    print_step "Building multi-platform image..."
    
    local build_cmd="docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --tag ${REGISTRY}${IMAGE_NAME}:${IMAGE_TAG}"
    
    if [ "$push_flag" = "push" ]; then
        build_cmd="$build_cmd --push"
        print_step "Will push to registry after build"
    else
        build_cmd="$build_cmd --load"
        print_warning "Multi-platform images cannot be loaded locally. Use --push to push to registry."
    fi
    
    eval "$build_cmd ."
    
    print_step "Multi-platform build completed"
}

# =============================================================================
# Main Script
# =============================================================================
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --local-amd64     Build for local AMD64 platform only"
    echo "  --local-arm64     Build for local ARM64 platform only"
    echo "  --multi           Build multi-platform image (requires registry push)"
    echo "  --multi-push      Build and push multi-platform image to registry"
    echo "  --tag TAG         Set image tag (default: latest)"
    echo "  --registry REG    Set registry prefix (e.g., your-registry.com/)"
    echo "  --help            Show this help message"
    echo
    echo "Examples:"
    echo "  $0 --local-amd64                    # Build for local AMD64"
    echo "  $0 --multi-push --tag v1.0.0        # Build and push multi-platform with tag"
    echo "  $0 --registry myregistry.com/       # Use custom registry"
}

main() {
    print_header
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --local-amd64)
                BUILD_TYPE="local-amd64"
                shift
                ;;
            --local-arm64)
                BUILD_TYPE="local-arm64"
                shift
                ;;
            --multi)
                BUILD_TYPE="multi"
                shift
                ;;
            --multi-push)
                BUILD_TYPE="multi-push"
                shift
                ;;
            --tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --registry)
                REGISTRY="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Default to local build if no option specified
    if [ -z "$BUILD_TYPE" ]; then
        BUILD_TYPE="local-amd64"
        print_warning "No build type specified, defaulting to --local-amd64"
    fi
    
    # Check prerequisites
    check_docker_buildx
    
    # Setup buildx if needed
    if [[ "$BUILD_TYPE" == "multi"* ]]; then
        setup_buildx
    fi
    
    # Execute build based on type
    case $BUILD_TYPE in
        "local-amd64")
            build_local "linux/amd64" "-amd64"
            ;;
        "local-arm64")
            build_local "linux/arm64" "-arm64"
            ;;
        "multi")
            build_multiplatform ""
            ;;
        "multi-push")
            if [ -z "$REGISTRY" ]; then
                print_error "Registry must be specified for push operations. Use --registry option."
                exit 1
            fi
            build_multiplatform "push"
            ;;
    esac
    
    print_step "Build process completed successfully!"
    echo
    echo "Image: ${REGISTRY}${IMAGE_NAME}:${IMAGE_TAG}"
    echo "Platform(s): $(case $BUILD_TYPE in 
        'local-amd64') echo 'linux/amd64' ;;
        'local-arm64') echo 'linux/arm64' ;;
        'multi'*) echo 'linux/amd64, linux/arm64' ;;
    esac)"
}

# Run main function with all arguments
main "$@"
