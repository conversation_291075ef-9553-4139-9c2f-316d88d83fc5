#!/bin/bash
# Docker Testing and Validation Script
# Tests the fraud detection Docker setup for functionality and multi-platform compatibility

set -e

# =============================================================================
# Configuration
# =============================================================================
IMAGE_NAME="fraud-detection"
IMAGE_TAG="latest"
TEST_PORT="8000"
HEALTH_ENDPOINT="http://localhost:${TEST_PORT}/health"
API_ENDPOINT="http://localhost:${TEST_PORT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# =============================================================================
# Helper Functions
# =============================================================================
print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}  Docker Testing and Validation Script${NC}"
    echo -e "${BLUE}  Enterprise Fund Flow Fraud Detection${NC}"
    echo -e "${BLUE}============================================${NC}"
    echo
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

wait_for_service() {
    local url=$1
    local timeout=${2:-60}
    local interval=5
    local elapsed=0
    
    print_test "Waiting for service at $url (timeout: ${timeout}s)"
    
    while [ $elapsed -lt $timeout ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            print_success "Service is responding"
            return 0
        fi
        
        echo -n "."
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    print_error "Service did not respond within ${timeout} seconds"
    return 1
}

cleanup_containers() {
    print_test "Cleaning up test containers..."
    docker stop fraud-test-container 2>/dev/null || true
    docker rm fraud-test-container 2>/dev/null || true
    print_success "Cleanup completed"
}

# =============================================================================
# Test Functions
# =============================================================================
test_image_exists() {
    print_test "Checking if Docker image exists..."
    
    if docker image inspect "${IMAGE_NAME}:${IMAGE_TAG}" >/dev/null 2>&1; then
        print_success "Docker image found: ${IMAGE_NAME}:${IMAGE_TAG}"
        return 0
    else
        print_error "Docker image not found: ${IMAGE_NAME}:${IMAGE_TAG}"
        print_warning "Please build the image first: ./docker-build.sh --local-amd64"
        return 1
    fi
}

test_container_start() {
    print_test "Testing container startup..."
    
    # Start container in background
    docker run -d \
        --name fraud-test-container \
        -p "${TEST_PORT}:8000" \
        "${IMAGE_NAME}:${IMAGE_TAG}" >/dev/null
    
    if [ $? -eq 0 ]; then
        print_success "Container started successfully"
        return 0
    else
        print_error "Failed to start container"
        return 1
    fi
}

test_health_endpoint() {
    print_test "Testing health endpoint..."
    
    if wait_for_service "$HEALTH_ENDPOINT" 60; then
        local response=$(curl -s "$HEALTH_ENDPOINT")
        if echo "$response" | grep -q "healthy\|ok\|status"; then
            print_success "Health endpoint is working"
            echo "Response: $response"
            return 0
        else
            print_warning "Health endpoint responded but format unexpected"
            echo "Response: $response"
            return 1
        fi
    else
        print_error "Health endpoint is not responding"
        return 1
    fi
}

test_api_endpoints() {
    print_test "Testing API endpoints..."
    
    # Test root endpoint
    local root_response=$(curl -s "${API_ENDPOINT}/")
    if echo "$root_response" | grep -q "虚假关联交易检测\|fraud"; then
        print_success "Root endpoint is working"
    else
        print_warning "Root endpoint response unexpected"
        echo "Response: $root_response"
    fi
    
    # Test docs endpoint
    if curl -s -f "${API_ENDPOINT}/docs" >/dev/null; then
        print_success "API documentation endpoint is accessible"
    else
        print_warning "API documentation endpoint is not accessible"
    fi
    
    # Test model info endpoint
    local model_response=$(curl -s "${API_ENDPOINT}/model/info")
    if echo "$model_response" | grep -q "model\|loaded\|info"; then
        print_success "Model info endpoint is working"
        echo "Model status: $model_response"
    else
        print_warning "Model info endpoint response unexpected"
    fi
}

test_prediction_api() {
    print_test "Testing prediction API..."
    
    # Sample prediction request
    local test_data='{
        "acntmcode": "COM0001",
        "partycode": "COM0002", 
        "amount": 100000.00,
        "or_type_fy": "转账",
        "quarter": "Q1",
        "is_holiday": false,
        "month_day": 15,
        "is_month_end": false
    }'
    
    local prediction_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$test_data" \
        "${API_ENDPOINT}/predict")
    
    if echo "$prediction_response" | grep -q "prediction\|fraud\|probability"; then
        print_success "Prediction API is working"
        echo "Prediction result: $prediction_response"
        return 0
    else
        print_warning "Prediction API response unexpected"
        echo "Response: $prediction_response"
        return 1
    fi
}

test_container_logs() {
    print_test "Checking container logs for errors..."
    
    local logs=$(docker logs fraud-test-container 2>&1)
    
    if echo "$logs" | grep -i "error\|exception\|failed" | grep -v "❌"; then
        print_warning "Found potential errors in logs:"
        echo "$logs" | grep -i "error\|exception\|failed" | head -5
    else
        print_success "No critical errors found in logs"
    fi
    
    # Show last few lines of logs
    echo "Last 10 lines of logs:"
    echo "$logs" | tail -10
}

test_multi_platform() {
    print_test "Testing multi-platform build capability..."
    
    if docker buildx version >/dev/null 2>&1; then
        print_success "Docker buildx is available for multi-platform builds"
        
        # List available platforms
        local platforms=$(docker buildx inspect --bootstrap 2>/dev/null | grep "Platforms:" | cut -d: -f2)
        if echo "$platforms" | grep -q "linux/amd64.*linux/arm64\|linux/arm64.*linux/amd64"; then
            print_success "Multi-platform support confirmed: $platforms"
        else
            print_warning "Limited platform support: $platforms"
        fi
    else
        print_warning "Docker buildx not available - multi-platform builds not supported"
    fi
}

# =============================================================================
# Main Test Suite
# =============================================================================
run_all_tests() {
    print_header
    
    local failed_tests=0
    
    # Pre-test cleanup
    cleanup_containers
    
    # Run tests
    test_image_exists || ((failed_tests++))
    test_multi_platform || ((failed_tests++))
    test_container_start || ((failed_tests++))
    test_health_endpoint || ((failed_tests++))
    test_api_endpoints || ((failed_tests++))
    test_prediction_api || ((failed_tests++))
    test_container_logs || ((failed_tests++))
    
    # Cleanup
    cleanup_containers
    
    # Summary
    echo
    echo -e "${BLUE}============================================${NC}"
    if [ $failed_tests -eq 0 ]; then
        print_success "All tests passed! Docker setup is working correctly."
    else
        print_warning "$failed_tests test(s) failed or had warnings."
        print_warning "Please review the output above for details."
    fi
    echo -e "${BLUE}============================================${NC}"
    
    return $failed_tests
}

# =============================================================================
# Command Line Interface
# =============================================================================
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --quick       Run quick tests only (image + startup)"
    echo "  --full        Run full test suite (default)"
    echo "  --build-test  Build image and run tests"
    echo "  --help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Run full test suite"
    echo "  $0 --quick           # Run quick tests only"
    echo "  $0 --build-test      # Build image first, then test"
}

main() {
    case "${1:-}" in
        --quick)
            print_header
            cleanup_containers
            test_image_exists && test_container_start && test_health_endpoint
            cleanup_containers
            ;;
        --build-test)
            print_test "Building image first..."
            ./docker-build.sh --local-amd64
            run_all_tests
            ;;
        --help)
            show_usage
            exit 0
            ;;
        --full|"")
            run_all_tests
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Trap to ensure cleanup on script exit
trap cleanup_containers EXIT

# Run main function
main "$@"
