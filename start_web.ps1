# 企业资金流向欺诈检测系统 - PowerShell启动脚本
# 解决CORS问题的完整版本

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "  企业资金流向欺诈检测系统" -ForegroundColor Yellow
Write-Host "  启动脚本 (解决CORS问题版本)" -ForegroundColor Yellow
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python环境
Write-Host "🔧 检查Python环境..." -ForegroundColor Green
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python已安装: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到Python，请先安装Python" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查依赖包
Write-Host "📦 检查依赖包..." -ForegroundColor Green
try {
    python -c "import fastapi, uvicorn" 2>$null
    Write-Host "✅ 依赖包已安装" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 缺少必要依赖，正在安装..." -ForegroundColor Yellow
    pip install fastapi uvicorn python-multipart
}

Write-Host ""
Write-Host "选择启动方式:" -ForegroundColor Cyan
Write-Host "1. 启动完整Web服务 (推荐 - 无CORS问题)" -ForegroundColor White
Write-Host "2. 启动原版API服务 (可能有CORS问题)" -ForegroundColor White
Write-Host "3. 仅训练模型" -ForegroundColor White
Write-Host "4. 测试修复" -ForegroundColor White
Write-Host "5. 退出" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请输入选择 (1-5)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "🚀 启动完整Web服务..." -ForegroundColor Green
        Write-Host "📱 浏览器将自动打开 http://localhost:9000/" -ForegroundColor Yellow
        Write-Host "⏸️  按 Ctrl+C 停止服务" -ForegroundColor Yellow
        Write-Host ""
        python web_server.py
    }
    "2" {
        Write-Host ""
        Write-Host "🚀 启动原版API服务..." -ForegroundColor Green
        Write-Host "📱 请手动打开: fraud_detection_web.html" -ForegroundColor Yellow
        Write-Host "⚠️  注意: 可能遇到CORS问题" -ForegroundColor Red
        Write-Host ""
        python fraud_api.py
    }
    "3" {
        Write-Host ""
        Write-Host "🤖 开始训练模型..." -ForegroundColor Green
        python tra_model.py
        Write-Host "✅ 模型训练完成" -ForegroundColor Green
    }
    "4" {
        Write-Host ""
        Write-Host "🧪 运行测试..." -ForegroundColor Green
        python test_fix.py
        Write-Host "✅ 测试完成" -ForegroundColor Green
    }
    "5" {
        Write-Host "👋 退出" -ForegroundColor Yellow
        exit 0
    }
    default {
        Write-Host "❌ 无效选择" -ForegroundColor Red
    }
}

Read-Host "按Enter键退出"
