# 虚假关联交易检测MVP - 完整版本
# 需要pandas/numpy/sklearn环境

import sys
import os
import importlib.util

# 导入必需的数据科学库
try:
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta
    import random
    import string
    print("✓ 成功导入基础库")
except ImportError as e:
    print(f"❌ 基础库导入失败: {e}")
    print("请安装: pip install pandas numpy")
    sys.exit(1)

# 机器学习相关导入
try:
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, roc_auc_score
    from sklearn.ensemble import RandomForestClassifier
    import xgboost as xgb
    print("✓ 成功导入机器学习库")
except ImportError as e:
    print(f"❌ 机器学习库导入失败: {e}")
    print("请安装: pip install scikit-learn xgboost")
    sys.exit(1)

# LLM增强功能依赖检查
try:
    import requests
    print("✓ 成功导入requests库（LLM功能需要）")
    LLM_AVAILABLE = True
except ImportError:
    print("⚠ requests库未安装，LLM增强功能不可用")
    print("如需使用LLM功能，请安装: pip install requests")
    LLM_AVAILABLE = False

# 获取当前文件目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 导入字段定义
spec0 = importlib.util.spec_from_file_location("fields", os.path.join(current_dir, "fields.py"))
if spec0 and spec0.loader:
    fields_module = importlib.util.module_from_spec(spec0)
    spec0.loader.exec_module(fields_module)
    MVP_FIELDS = fields_module.MVP_FIELDS
    print("✓ 成功导入字段定义")

# 动态导入gen_data.py模块
spec1 = importlib.util.spec_from_file_location("gen_data", os.path.join(current_dir, "gen_data.py"))
if spec1 and spec1.loader:
    gen_data_module = importlib.util.module_from_spec(spec1)
    spec1.loader.exec_module(gen_data_module)
    MVPDataGenerator = gen_data_module.MVPDataGenerator
    print("✓ 成功导入MVPDataGenerator")

# 动态导入tra_model.py模块  
spec2 = importlib.util.spec_from_file_location("tra_model", os.path.join(current_dir, "tra_model.py"))
if spec2 and spec2.loader:
    tra_model_module = importlib.util.module_from_spec(spec2)
    spec2.loader.exec_module(tra_model_module)
    MVPFraudDetector = tra_model_module.MVPFraudDetector
    print("✓ 成功导入MVPFraudDetector")

# 动态导入LLM增强数据生成器
if LLM_AVAILABLE:
    try:
        spec3 = importlib.util.spec_from_file_location("llm_enhanced_gen_data", os.path.join(current_dir, "llm_enhanced_gen_data.py"))
        if spec3 and spec3.loader:
            llm_gen_data_module = importlib.util.module_from_spec(spec3)
            spec3.loader.exec_module(llm_gen_data_module)
            LLMEnhancedDataGenerator = llm_gen_data_module.LLMEnhancedDataGenerator
            print("✓ 成功导入LLMEnhancedDataGenerator")
        else:
            print("⚠ 未找到LLM增强数据生成器文件")
            LLM_AVAILABLE = False
    except Exception as e:
        print(f"⚠ LLM增强数据生成器导入失败: {e}")
        LLM_AVAILABLE = False
else:
    print("⚠ LLM增强功能不可用，将仅提供标准功能")

def run_mvp_demo():
    """运行MVP演示 - 完整版本"""
    
    print("=== 虚假关联交易检测MVP (完整版) ===\n")
    
    # 0. 展示字段定义
    print("0. MVP字段定义:")
    for table_name, fields in MVP_FIELDS.items():
        print(f"  {table_name}: {len(fields)} 个字段")
    print()
    
    # 1. 生成模拟数据（增加规模和复杂性）
    print("1. 生成模拟数据...")
    generator = MVPDataGenerator()
    
    settlement_df = generator.generate_settlement_data(n_total=8000, fraud_ratio=0.03)  # 8000条，3%欺诈率
    receivable_df = generator.generate_receivable_data(500)  # 增加到500条
    payable_df = generator.generate_payable_data(500)
    guarantee_df = generator.generate_guarantee_data(150)  # 增加到150条
    truth_labels = generator.generate_truth_labels(settlement_df)
    
    print(f"资金结算数据: {len(settlement_df)} 条")
    print(f"应收款项数据: {len(receivable_df)} 条")
    print(f"应付款项数据: {len(payable_df)} 条")
    print(f"担保数据: {len(guarantee_df)} 条")
    print(f"真实标签: {truth_labels['is_fraud'].sum()} 个欺诈交易")
    print(f"欺诈比例: {truth_labels['is_fraud'].mean():.1%}")
    print(f"涉及企业数量: {settlement_df['acntmcode'].nunique()} 家\n")
    
    # 2. 特征工程
    print("2. 构建特征...")
    detector = MVPFraudDetector()
    features_df = detector.create_mvp_features(settlement_df, receivable_df, payable_df, guarantee_df)
    print(f"生成特征矩阵: {features_df.shape}\n")
    
    # 3. 模型训练
    print("3. 训练模型...")
    results = detector.train_mvp_model(features_df, truth_labels, settlement_df)
    
    # 4. 展示可疑案例
    print("\n=== 高风险案例 ===")
    high_risk = results['test_predictions'][results['test_predictions']['predicted_proba'] > 0.7]
    print(high_risk.head(10))
    
    # 5. 保存结果
    settlement_df.to_csv('mvp_settlement_data.csv', index=False)
    features_df.to_csv('mvp_features.csv', index=False)
    truth_labels.to_csv('mvp_truth_labels.csv', index=False)
    
    print(f"\n=== MVP验证完成 ===")
    print(f"模型AUC: {results['auc']:.4f}")
    print("数据已保存到CSV文件")

def run_llm_enhanced_mvp_demo(api_token="sk-your-token"):
    """运行LLM增强版MVP演示"""
    
    print("=== 虚假关联交易检测MVP (LLM增强版) ===\n")
    
    # 检查是否有LLM生成器
    if not LLM_AVAILABLE:
        print("❌ LLM增强功能不可用，请安装requests库或检查依赖")
        print("转为运行标准版本...")
        run_mvp_demo()
        return
    
    # 0. 展示字段定义
    print("0. MVP字段定义:")
    for table_name, fields in MVP_FIELDS.items():
        print(f"  {table_name}: {len(fields)} 个字段")
    print()
    
    # 1. 使用LLM增强数据生成
    print("1. 使用LLM生成增强模拟数据...")
    llm_generator = LLMEnhancedDataGenerator(api_token=api_token)
    
    # 生成LLM增强的资金结算数据
    settlement_df = llm_generator.generate_llm_enhanced_settlement_data(
        n_total=2000, fraud_ratio=0.05, batch_size=10  # 减小批次大小避免API限流
    )
    
    # 生成其他数据（保持兼容）
    receivable_df = llm_generator.generate_receivable_data(200)
    payable_df = llm_generator.generate_payable_data(200)
    guarantee_df = llm_generator.generate_guarantee_data(80)
    truth_labels = llm_generator.generate_truth_labels(settlement_df)
    
    print(f"\n📊 LLM增强数据生成完成:")
    print(f"资金结算数据: {len(settlement_df)} 条")
    print(f"  - LLM生成: {settlement_df['llm_generated'].sum() if 'llm_generated' in settlement_df.columns else 0} 条")
    print(f"  - 传统生成: {len(settlement_df) - settlement_df['llm_generated'].sum() if 'llm_generated' in settlement_df.columns else len(settlement_df)} 条")
    print(f"应收款项数据: {len(receivable_df)} 条")
    print(f"应付款项数据: {len(payable_df)} 条")
    print(f"担保数据: {len(guarantee_df)} 条")
    print(f"真实标签: {truth_labels['is_fraud'].sum()} 个欺诈交易")
    print(f"欺诈比例: {truth_labels['is_fraud'].mean():.1%}")
    print(f"涉及企业数量: {settlement_df['acntmcode'].nunique()} 家\n")
    
    # 2. 特征工程
    print("2. 构建特征...")
    detector = MVPFraudDetector()
    features_df = detector.create_mvp_features(settlement_df, receivable_df, payable_df, guarantee_df)
    print(f"生成特征矩阵: {features_df.shape}\n")
    
    # 3. 模型训练
    print("3. 训练模型...")
    results = detector.train_mvp_model(features_df, truth_labels, settlement_df)
    
    # 4. 分析LLM生成数据的质量
    print("\n=== LLM数据质量分析 ===")
    if 'llm_generated' in settlement_df.columns:
        llm_data = settlement_df[settlement_df['llm_generated'] == True]
        traditional_data = settlement_df[settlement_df['llm_generated'] == False]
        
        print(f"LLM生成数据统计:")
        print(f"  平均金额: {llm_data['amount'].mean():.2f}")
        print(f"  金额标准差: {llm_data['amount'].std():.2f}")
        print(f"  欺诈比例: {llm_data[llm_data['transaction_type'].str.startswith('fraud')].shape[0] / len(llm_data):.1%}")
        
        print(f"传统生成数据统计:")
        print(f"  平均金额: {traditional_data['amount'].mean():.2f}")
        print(f"  金额标准差: {traditional_data['amount'].std():.2f}")
        print(f"  欺诈比例: {traditional_data[traditional_data['transaction_type'].str.startswith('fraud')].shape[0] / len(traditional_data):.1%}")
    
    # 5. 展示可疑案例（优先展示LLM生成的）
    print("\n=== 高风险案例 ===")
    high_risk = results['test_predictions'][results['test_predictions']['predicted_proba'] > 0.7]
    print(high_risk.head(10))
    
    # 6. 保存结果
    settlement_df.to_csv('mvp_llm_settlement_data.csv', index=False)
    features_df.to_csv('mvp_llm_features.csv', index=False)
    truth_labels.to_csv('mvp_llm_truth_labels.csv', index=False)
    
    print(f"\n=== LLM增强MVP验证完成 ===")
    print(f"模型AUC: {results['auc']:.4f}")
    print("数据已保存到CSV文件（带llm前缀）")
    print("建议对比传统方法与LLM增强方法的效果差异")

if __name__ == "__main__":
    import sys
    
    print("=== 虚假关联交易检测MVP ===")
    print("请选择运行模式:")
    print("1. 标准版本 (传统数据生成)")
    if LLM_AVAILABLE:
        print("2. LLM增强版本 (需要API Token)")
        print("3. 两种版本对比")
        print("4. 运行LLM功能测试")
    else:
        print("2. LLM增强版本 (不可用 - 缺少依赖)")
        print("请安装: pip install requests")
    print("5. 启动API服务")
    print("6. 测试API服务")
    
    # 简单的命令行参数处理
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        mode = input("请输入选择 (1/2/3/4/5/6): ").strip()
    
    if mode == "1":
        print("\n运行标准版本...")
        run_mvp_demo()
    elif mode == "2":
        if not LLM_AVAILABLE:
            print("❌ LLM功能不可用，请安装依赖: pip install requests")
        else:
            print("\n运行LLM增强版本...")
            # 从环境变量或用户输入获取API Token
            import os
            api_token = os.environ.get('SILICONFLOW_API_TOKEN')
            if not api_token:
                api_token = input("请输入SiliconFlow API Token (或按Enter使用默认值): ").strip()
                if not api_token:
                    api_token = "sk-dceyienvrazgavqubkwwxtlmsbseqlgmswqlowjfbbiyeenl"  # 默认值，需要用户替换
                    print("⚠ 使用默认Token，请确保已正确配置")
            
            run_llm_enhanced_mvp_demo(api_token)
    elif mode == "3":
        if not LLM_AVAILABLE:
            print("❌ LLM功能不可用，无法进行对比测试")
        else:
            print("\n运行对比测试...")
            print("\n--- 标准版本 ---")
            run_mvp_demo()
            
            print("\n\n--- LLM增强版本 ---")
            api_token = input("请输入SiliconFlow API Token: ").strip()
            if not api_token:
                print("❌ 需要API Token才能运行LLM增强版本")
            else:
                run_llm_enhanced_mvp_demo(api_token)
    elif mode == "4":
        if not LLM_AVAILABLE:
            print("❌ LLM功能不可用，无法运行测试")
        else:
            print("\n运行LLM功能测试...")
            import subprocess
            test_script = os.path.join(current_dir, "test_llm.py")
            if os.path.exists(test_script):
                subprocess.run([sys.executable, test_script])
            else:
                print("❌ 测试脚本不存在")
    elif mode == "5":
        print("\n启动API服务...")
        try:
            import subprocess
            api_script = os.path.join(current_dir, "fraud_api.py")
            if os.path.exists(api_script):
                print("正在启动FastAPI服务...")
                print("API地址: http://localhost:8000")
                print("API文档: http://localhost:8000/docs")
                print("按 Ctrl+C 停止服务")
                subprocess.run([sys.executable, api_script])
            else:
                print("❌ API服务脚本不存在")
        except KeyboardInterrupt:
            print("\n服务已停止")
        except Exception as e:
            print(f"❌ 启动API服务失败: {e}")
    elif mode == "6":
        print("\n测试API服务...")
        try:
            import subprocess
            test_script = os.path.join(current_dir, "test_api.py")
            if os.path.exists(test_script):
                subprocess.run([sys.executable, test_script])
            else:
                print("❌ API测试脚本不存在")
        except Exception as e:
            print(f"❌ API测试失败: {e}")
    else:
        print("无效选择，运行标准版本...")
        run_mvp_demo()