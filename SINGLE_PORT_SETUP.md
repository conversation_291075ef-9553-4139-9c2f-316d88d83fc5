# 🚀 Single-Port Docker Setup

The Docker container now serves **both** the FastAPI backend and static web files on the **same port 8000**, providing a simplified single-port solution.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────┐
│           Docker Container             │
├─────────────────────────────────────────┤
│  ┌─────────────────────────────────────┐ │
│  │         FastAPI Service             │ │
│  │                                     │ │
│  │  ┌─────────────┐ ┌─────────────────┐│ │
│  │  │ API Routes  │ │ Static Files    ││ │
│  │  │ /predict    │ │ /fraud_*.html   ││ │
│  │  │ /health     │ │ /static/*       ││ │
│  │  │ /docs       │ │                 ││ │
│  │  └─────────────┘ └─────────────────┘│ │
│  │                                     │ │
│  │           Port 8000                 │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🎯 What's Included

### Single Port 8000 - Integrated Service
- **API Endpoints**: All fraud detection API functionality
- **Interactive Docs**: Swagger UI at `/docs`
- **Health Check**: Status endpoint at `/health`
- **Web Interface**: Direct access to `fraud_detection_web.html`
- **Demo Interface**: Direct access to `fraud_detection_demo.html`
- **Static Files**: All files served via `/static/` mount
- **Model Operations**: Prediction, training, batch processing

## 🔧 Implementation Details

### FastAPI Static File Integration
- **StaticFiles Mount**: `/static/` serves all project files
- **Direct HTML Routes**: HTML files accessible at root level
- **CORS Enabled**: Cross-origin requests allowed
- **Relative Paths**: Web interface uses relative API calls

### Simplified Architecture
- **Single Process**: Only FastAPI service runs
- **Integrated Serving**: API and static files from same server
- **No Port Conflicts**: Everything on port 8000
- **Simplified Networking**: No inter-service communication needed

### Docker Configuration
- **Single Port**: Only 8000 exposed
- **Simplified Health Check**: Tests both API and static serving
- **Reduced Complexity**: No process management needed
- **Better Resource Usage**: Single service, lower overhead

## 🚀 Usage Examples

### Basic Usage
```bash
# Start integrated service
docker run -p 8000:8000 fraud-detection:latest

# Access everything from port 8000
curl http://localhost:8000/health                    # API health check
curl http://localhost:8000/fraud_detection_web.html  # Web interface
open http://localhost:8000/docs                      # API documentation
```

### Docker Compose
```bash
# Start with compose
docker-compose up

# Everything available at:
# - API: http://localhost:8000
# - Web: http://localhost:8000/fraud_detection_web.html
```

### Development Mode
```bash
# Mount source code for development
docker run -p 8000:8000 \
  -v $(pwd):/app \
  fraud-detection:latest
```

## 🔍 Service Endpoints

### API Endpoints
- `GET /` - Service information with web interface links
- `GET /health` - Health check
- `GET /docs` - Interactive API documentation
- `POST /predict` - Single prediction
- `POST /predict/batch` - Batch predictions
- `POST /train` - Model training
- `GET /model/info` - Model information

### Web Interface Endpoints
- `GET /fraud_detection_web.html` - Main web interface
- `GET /fraud_detection_demo.html` - Demo interface
- `GET /static/` - Static file directory listing
- `GET /static/{filename}` - Individual static files

## 🛠️ Configuration

### Environment Variables
```bash
# Service configuration
API_PORT=8000          # FastAPI port (default)
API_HOST=0.0.0.0       # FastAPI bind address

# Application configuration
SILICONFLOW_API_TOKEN=your-token
DEBUG=false
LOG_LEVEL=INFO
```

### Web Interface Configuration
The web interface now uses relative paths for API calls:
```javascript
// Old (dual-port) configuration
const API_BASE_URL = window.location.protocol === 'file:' ? 'http://localhost:8000' : '/api';

// New (single-port) configuration
const API_BASE_URL = '';  // Relative paths to same origin
```

## 🧪 Testing

### Automated Testing
```bash
# Run comprehensive tests
./docker-test.sh

# Quick single-port test
./test-dual-service.sh  # Now tests single-port setup
```

### Manual Testing
```bash
# Test API service
curl -X POST http://localhost:8000/predict \
  -H "Content-Type: application/json" \
  -d '{"acntmcode":"COM001","partycode":"COM002","amount":100000,"or_type_fy":"转账","quarter":"Q1","is_holiday":false,"month_day":15,"is_month_end":false}'

# Test web interface
curl http://localhost:8000/fraud_detection_web.html

# Test static files
curl http://localhost:8000/static/README.md
```

## 🔧 Troubleshooting

### Common Issues

#### Web Interface Not Loading
```bash
# Check if HTML files exist in container
docker exec <container-name> ls -la /app/*.html

# Check FastAPI logs
docker logs <container-name>
```

#### API Calls Failing from Web Interface
```bash
# Verify relative paths are working
# Open browser dev tools and check network requests
# Should see requests to same origin (localhost:8000)
```

#### Static Files Not Found
```bash
# Check static mount
curl http://localhost:8000/static/

# Verify files are accessible
docker exec <container-name> ls -la /app/
```

### Health Checks
```bash
# Check integrated service
curl http://localhost:8000/health

# Check web interface serving
curl http://localhost:8000/fraud_detection_web.html

# Container health status
docker ps  # Shows health status
```

## 🎉 Benefits

### Simplified Deployment
- ✅ Single port configuration
- ✅ No port conflicts
- ✅ Easier firewall rules
- ✅ Simplified load balancing

### Reduced Complexity
- ✅ No inter-service communication
- ✅ Single process to monitor
- ✅ Simplified networking
- ✅ Lower resource usage

### Better User Experience
- ✅ Same origin for API and web
- ✅ No CORS issues
- ✅ Simplified URLs
- ✅ Consistent base URL

### Development Friendly
- ✅ Easier local development
- ✅ Simplified testing
- ✅ Single service to debug
- ✅ Clear architecture

## 🔄 Migration from Dual-Port

If you were using the previous dual-port setup:

### Update Docker Commands
```bash
# Old
docker run -p 8000:8000 -p 9000:9000 fraud-detection:latest

# New
docker run -p 8000:8000 fraud-detection:latest
```

### Update Web Interface URLs
```bash
# Old URLs
http://localhost:8000/docs           # API docs
http://localhost:9000/fraud_detection_web.html  # Web interface

# New URLs
http://localhost:8000/docs           # API docs
http://localhost:8000/fraud_detection_web.html  # Web interface
```

### Update Bookmarks/Scripts
- Change any references from port 9000 to port 8000
- Update any automation scripts
- Modify any reverse proxy configurations

This single-port setup provides a cleaner, simpler deployment while maintaining all the functionality of the previous dual-port configuration! 🎯
