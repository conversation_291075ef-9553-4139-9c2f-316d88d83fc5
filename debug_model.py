#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型加载调试脚本
"""

import sys
import os
import traceback

def test_model_loading():
    """测试模型加载"""
    try:
        print("=" * 50)
        print("🔧 模型加载调试测试")
        print("=" * 50)
        
        # 检查文件存在性
        print("\n1. 检查模型文件...")
        model_files = [
            'models/fraud_detection_model.pkl',
            'models/feature_columns.pkl', 
            'models/model_metadata.pkl'
        ]
        
        for file_path in model_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file_path} (大小: {size} bytes)")
            else:
                print(f"  ❌ {file_path} - 文件不存在")
                return False
        
        # 导入模块
        print("\n2. 导入tra_model模块...")
        try:
            from tra_model import MVPFraudDetector
            print("  ✅ tra_model导入成功")
        except Exception as e:
            print(f"  ❌ tra_model导入失败: {e}")
            return False
        
        # 创建检测器
        print("\n3. 创建检测器实例...")
        try:
            detector = MVPFraudDetector()
            print("  ✅ 检测器创建成功")
        except Exception as e:
            print(f"  ❌ 检测器创建失败: {e}")
            traceback.print_exc()
            return False
        
        # 加载模型
        print("\n4. 加载模型...")
        try:
            result = detector.load_model()
            if result:
                print("  ✅ 模型加载成功")
                
                # 检查各组件
                print("\n5. 检查模型组件...")
                if detector.model is not None:
                    print(f"  ✅ 主模型: {type(detector.model)}")
                else:
                    print("  ❌ 主模型为空")
                
                if detector.feature_columns is not None:
                    print(f"  ✅ 特征列: {len(detector.feature_columns)} 个")
                    print(f"    前5个特征: {detector.feature_columns[:5]}")
                else:
                    print("  ❌ 特征列为空")
                
                if detector.model_metadata is not None:
                    print(f"  ✅ 元数据: {type(detector.model_metadata)}")
                    print(f"    元数据键: {list(detector.model_metadata.keys())}")
                    
                    # 测试序列化
                    print("\n6. 测试元数据序列化...")
                    try:
                        from fraud_api import serialize_for_json
                        serialized = serialize_for_json(detector.model_metadata)
                        print("  ✅ 序列化成功")
                        print(f"    序列化后键: {list(serialized.keys())}")
                    except Exception as serialize_error:
                        print(f"  ❌ 序列化失败: {serialize_error}")
                        traceback.print_exc()
                        return False
                        
                else:
                    print("  ❌ 元数据为空")
                
                return True
            else:
                print("  ❌ 模型加载返回False")
                return False
                
        except Exception as e:
            print(f"  ❌ 模型加载异常: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n💥 测试失败！")
